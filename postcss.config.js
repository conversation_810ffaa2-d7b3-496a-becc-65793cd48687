module.exports = {
  plugins: {
    "postcss-pxtorem": {
      // "rootValue": 37.5, // 设计稿宽度的1/10，代表 1rem=37.5px
      // 配置在将px转化为rem时 1rem等于多少px(因为我们搭配使用了amfe-flexible插件 此处我们需要设置的值应是UI设计稿全屏基准宽度的十分之一)
      // 当UI设计稿的全屏基准宽度是750px时 此处设置的值为75 但项目中使用了vant组件库 vant的设计稿总宽度是375px 其十分之一应是37.5(需要区分设置)
      rootValue ({ file }) {
        return file.indexOf('vant') !== -1 ? 37.5 : 37.5
      },
      "propList": ["*"], // 需要做转化处理的css属性  * 就是所有属性都要转换，如`hight`、`width`、`margin`等，`*`表示全部
      // 若想设置部分样式不转化 可以在配置项中写出
      // eg: 除 border和font-size外 所有px均转化为rem
      // propList: ["*", "!border","!font-size"], 
      // "exclude": /node_modules/i, // 这里表示不处理node_modules文件下的css
      "selectorBlackList": ["ignore-"], // 忽略的选择器前缀
      "mediaQuery": false, // 是否在媒体查询中也转换 px
      "minPixelValue": 1, // 最小的 px 值才转换为 rem
      "replace": true, // 是否更换属性值，而不是添加一个rem的新属性
      "unitPrecision": 5 // 单位转换后的精度
    }
  }
}