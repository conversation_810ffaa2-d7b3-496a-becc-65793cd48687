<template>
  <router-view></router-view>
</template>
<script setup>
import api from '@/api'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import VConsole from 'vconsole'
const route = useRoute()
// 初始化vConsole调试工具
let vConsole = null
onMounted(() => {
  getConfig()
})
const getConfig = async () => {
  const res = await api.globalReadOpenConfig({ codes: ['ixian_log'] })
  if (res.data.ixian_log == '1') {
    if (!vConsole) {
      vConsole = new VConsole()
      console.log(`${route.name}页面中🔧 vConsole已启用`)
    }
  }
}
</script>
<style lang="scss">
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: Microsoft YaHei;
  background-color: #fff;
}
</style>
