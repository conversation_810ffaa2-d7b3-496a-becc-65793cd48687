// 导入封装的方法
import HTTP from '@/http'
import GlobalApi from '@/http/GlobalApi'
const api = {
  ...GlobalApi,
  tableHeadInfo (params) { // 自定义表头 列详情
    return HTTP.get(`/customColumn/info/${params}`)
  },
  newsColumnList (params) { // 资讯栏目
    return HTTP.json('/newsColumn/app/list', params)
  },
  newsContentTopList (params) { // 资讯置顶轮播资讯
    return HTTP.json('/newsContent/app/topList', params)
  },
  newsContentList (params) { // 资讯列表
    return HTTP.json('/newsContent/list?', params)
  },
  newsContentInfo (params) { // 资讯详情
    return HTTP.json('/newsContent/info', params)
  },
  consultActivityList (params) { // 协商活动列表
    return HTTP.json('/consultActivity/list', params)
  },
  consultActivityInfo (params) { // 协商活动详情
    return HTTP.json('/consultActivity/info', params)
  },
  opinioncollectList (params) { // 网络议政列表
    return HTTP.json('/opinioncollect/listapp', params)
  },
  opinioncollectInfo (params) { // 网络议政详情
    return HTTP.json('/opinioncollect/info', params)
  },
  commentList (params) { // 网络议政评论列表
    return HTTP.json('/comment/twoLevelTree', params)
  },
  commentAdd (params) { // 网络议政发表评论
    return HTTP.json('/comment/add', params)
  },
  getCount (params) { // 网络议政评论和点赞数量
    return HTTP.json('/praises/count', params)
  },
  getLikeAdd (params) { // 网络议政点赞
    return HTTP.json('/praises/add', params)
  },
  getLikeDel (params) { // 网络议政取消点赞
    return HTTP.json('/praises/dels', params)
  },
  commentDels (params) { // 网络议政评论删除
    return HTTP.json('/comment/dels', params)
  },
  studypaperList (params) { // 问卷调查列表
    return HTTP.json('/studypaper/mylist', params)
  },
  studyexamineAdd (params) { // 考试
    return HTTP.json('/studyexamine/add', params)
  },
  studypapertopicMytopics (params) { // 考试
    return HTTP.json('/studypapertopic/mytopics', params)
  },
  studypaperExampaper (params) { // 考试
    return HTTP.json('/studypaper/exampaper', params)
  },
  studyexamineSubmitquestion (params) { // 重新提交
    return HTTP.json('/studyexamine/submitquestion', params)
  },
  officeOnlineTopicList (params) { // 委员会客厅列表
    return HTTP.json('/officeOnlineTopic/list', params)
  },
  officeOnlineTopicInfo (params) { // 委员会客厅列表
    return HTTP.json('/officeOnlineTopic/info', params)
  },
  themeLetterWithAnswer (params) { // 委员会客厅评论列表
    return HTTP.json('/themeLetter/withAnswer', params)
  },
  themeLetterAdd (params) { // 委员会客厅提交留言
    return HTTP.json('/themeLetter/add', params)
  },
  newsContentBatchList (params) { // 委员会客厅提交留言
    return HTTP.json('/newsContent/app/batchList', params)
  },
  notificationList (params) { // 公告列表
    return HTTP.json('/notification/list', params)
  },
  notificationInfo (params) { // 公告详情
    return HTTP.json('/notification/info', params)
  },
  historicalAccountsCollectAdd (params) { // 文史资料采集新增
    return HTTP.json('/historicalAccountsCollect/add', params)
  },
  historicalAccountsStructureList (params) { // 获取史料类别
    return HTTP.json('/historicalAccountsStructure/list', params)
  },
  ixaToekn (params) {
    return HTTP.json('/userInfo/getJsapiToken', params)
  },
  ixaUser (params) {
    return HTTP.json('/userInfo/getUser', params)
  },

  // ------------------
  getStationUserInfo (params) { // 站点用户信息
    return HTTP.json('/zyConvenientlyPat/getStationUserInfo', params)
  },
  zyConvenientlyPatList (params) { // 列表
    return HTTP.json('/zyConvenientlyPat/list', params)
  },
  zyConvenientlyPatInfo (params) { // 详情
    return HTTP.json('/zyConvenientlyPat/info', params)
  },
  findNearbyWorkerStation (params) { // 通过经纬度获取联络站
    return HTTP.json('/zyConvenientlyPat/findNearbyWorkerStation', params)
  },
  zyConvenientlyPatAdd (params) { // 新增
    return HTTP.json('/zyConvenientlyPat/add', params)
  },
  zyConvenientlyPatEdit (params) { //编辑
    return HTTP.json('/zyConvenientlyPat/edit', params)
  },
  zyConvenientlyPatDels (params) { //编辑
    return HTTP.json('/zyConvenientlyPat/dels', params)
  },
  attachmentFileAdd (params) {
    return HTTP.json('/attachmentFile/add', params)
  },
  zyConvenientlyBrowseAdd (params) {
    return HTTP.json('/zyConvenientlyBrowse/add', params)
  },
  zyConvenientlyBrowseDels (params) {
    return HTTP.json('/zyConvenientlyBrowse/dels', params)
  },
  zyConvenientlyCommentList (params) {
    return HTTP.json('/zyConvenientlyComment/list', params)
  },
  zyConvenientlyCommentTreeList (params) {
    return HTTP.json('/zyConvenientlyComment/treeList', params)
  },
  zyConvenientlyCommentAdd (params) {
    return HTTP.json('/zyConvenientlyComment/add', params)
  },
  zyConvenientlyCommentDels (params) {
    return HTTP.json('/zyConvenientlyComment/dels', params)
  },
  zyConvenientlyPatAnswer (params) {
    return HTTP.json('/zyConvenientlyPat/answer', params)
  },
  publicZyConvenientlyPat (params) {
    return HTTP.json('/zyConvenientlyPat/publicZyConvenientlyPat', params)
  },
  clueZyConvenientlyPat (params) {
    return HTTP.json('/zyConvenientlyPat/clueZyConvenientlyPat', params)
  },
  assignZyConvenientlyPat (params) {
    return HTTP.json('/zyConvenientlyPat/assignZyConvenientlyPat', params)
  },
  assign12345ZyConvenientlyPat (params) {
    return HTTP.json('/zyConvenientlyPat/assign12345ZyConvenientlyPat', params)
  }
}
export default api
