import axios from 'axios'
import { SM4 } from 'gm-crypto'
import AES from 'crypto-js/aes'
import pad from 'crypto-js/pad-pkcs7'
import mode from 'crypto-js/mode-ecb'
import encHex from 'crypto-js/enc-hex'
import encUtf8 from 'crypto-js/enc-utf8'
import encBase64 from 'crypto-js/enc-base64'

import md5 from 'crypto-js/md5'
const SECRET_IV = encUtf8.parse('qazwsxedcrfv0000')
const SECRET_KEY = encUtf8.parse('qazwsxedcrfv0000')
const VUE_APP_SECRET_KEY = '42a6503ed517bb49b78c4b199f6001e1cf6ced30674e2296c65bad2419755e57'
const VUE_APP_SECRET_KEY_ONE = '9d74ccc271c7a9fca1605fa2d9c0090dcf6ced30674e2296c65bad2419755e57'
const VUE_APP_SECRET_KEY_TWO = '079d9d88b05861f03f13ad091d88e744cf6ced30674e2296c65bad2419755e57'
const stringToHex = (str) => Array.prototype.map.call(str, (c) => c.charCodeAt(0).toString(16)).join('')
/**
* 加密方法
* @param data - 数据
*/
const encryptKey = (data) => {
  const dataHex = encUtf8.parse(data)
  const encrypted = AES.encrypt(dataHex, SECRET_KEY, { iv: SECRET_IV, mode: mode, padding: pad })
  return encrypted.ciphertext.toString()
}
/**
* 解密方法
* @param data - 数据
*/
const decryptKey = (data) => {
  const encryptedHexStr = encHex.parse(data)
  const str = encBase64.stringify(encryptedHexStr)
  const decrypt = AES.decrypt(str, SECRET_KEY, { iv: SECRET_IV, mode: mode, padding: pad })
  const decryptedStr = decrypt.toString(encUtf8)
  return decryptedStr.toString()
}
/**
* 加密方法
* @param data - 数据
*/
const gm_encrypt = (data, key, iv) => {
  const ivUtil = stringToHex(iv || 'zysoft2025518888')
  const keyUtil = stringToHex(key || 'zysoft2025518888')
  const sm4Util = SM4.encrypt(data, keyUtil, {
    iv: ivUtil, mode: SM4.constants.CBC, inputEncoding: 'utf8', outputEncoding: 'base64'
  })
  return sm4Util
}
/**
* 解密方法
* @param data - 数据
*/
const gm_decrypt = (data, key, iv) => {
  const ivUtil = stringToHex(iv || 'zysoft2025518888')
  const keyUtil = stringToHex(key || 'zysoft2025518888')
  const sm4Util = SM4.decrypt(data, keyUtil, {
    iv: ivUtil, mode: SM4.constants.CBC, inputEncoding: 'base64', outputEncoding: 'utf8'
  })
  return sm4Util
}
export default {
  gm_encrypt,
  gm_decrypt,
  encryptKey,
  decryptKey,
  // 十六位十六进制数作为密钥偏移量
  encrypt (word, keyStr, type) {
    let key = encUtf8.parse(keyStr)
    if (type && type === '1') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY))
    }
    if (type && type === '2') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_ONE))
    }
    if (type && type === '3') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_TWO))
    }
    const srcs = encUtf8.parse(word)
    const encrypted = AES.encrypt(srcs, key, { mode: mode, padding: pad })
    return encrypted.toString()
  },
  // 解密
  decrypt (word, keyStr, type) {
    let key = encUtf8.parse(keyStr)
    if (type && type === '1') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY))
    }
    if (type && type === '2') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_ONE))
    }
    if (type && type === '3') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_TWO))
    }
    const decrypt = AES.decrypt(word, key, { mode: mode, padding: pad })
    return encUtf8.stringify(decrypt).toString()
  },
  // 加密
  Md5Js (text) {
    return md5(text).toString()
  },
  tmp (type) {
    var tmp = Date.parse(new Date()).toString()
    if (type) {
      tmp = tmp.substr(0, 10)
    }
    return Number(tmp)
  },
  tmpNte () {
    let date = new Date()
    const Y = date.getFullYear()
    const M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
    const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
    const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
    const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
    const seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
    date = Y + '' + M + '' + D + '' + hours + '' + minutes + '' + seconds
    return date
  }
}
// 限制只能输入数字
export const validNum = value => value.replace(/[^0-9]/g, '')
// 去除输入的空格
export const deleteBlank = value => value.replace(/\s+/g, '')
// 限制只能输入英文和数字
export const englishNum = value => value.replace(/[^\w\.\/]/gi, '') // eslint-disable-line
// 限制不能输入特殊字符
export const validForbid = value => value.replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g, '').replace(/\s/g, '')
// 手机号码的中间四位替换为星号*
export const encryptPhone = value => value ? value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''
/**
 * @description: 计算文件大小
 * @param {String} size 文件大小
 * @return  {String} B KB MB GB
 */
export const size2Str = size => {
  size = Number(size)
  if (size < 1024) {
    return size + 'B'
  } else if (size >= 1024 && size < Math.pow(1024, 2)) {
    return parseFloat(size / 1024).toFixed(2) + 'KB'
  } else if (size >= Math.pow(1024, 2) && size < Math.pow(1024, 3)) {
    return parseFloat(size / Math.pow(1024, 2)).toFixed(2) + 'MB'
  } else if (size > Math.pow(1024, 3)) {
    return parseFloat(size / Math.pow(1024, 3)).toFixed(2) + 'GB'
  } else {
    return 0 + 'B'
  }
}

export const timePoor = data => {
  const o = new Date().getTime()
  const n = new Date(data).getTime()
  const f = Math.abs(n - o)
  if (f < 6e4) {
    return '1分钟'
  }
  if (f < 36e5) {
    return parseInt(f / 6e4) + '分钟'
  }
  if (f < 864e5) {
    return parseInt(f / 36e5) + '小时'
  }
  if (f < 2592e6) {
    return parseInt(f / 864e5) + '天'
  }
  if (f < 31536e6) {
    return parseInt(f / 2592e6) + '个月'
  }
  return parseInt(f / 31536e6) + '年'
}

export const numberToString = number => {
  number = number + ''
  if (number.match(/\D/) || number.length >= 14) return
  const zhArray = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'] // 数字对应中文
  const baseArray = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万'] //进位填充字符，第一位是 个位，可省略
  let string = String(number)
    .split('')
    .reverse()
    .map((item, index) => {
      // 把数字切割成数组并倒序排列，然后进行遍历转成中文
      // 如果当前位为0，直接输出数字， 否则输出 数字 + 进位填充字符
      item = Number(item) == 0 ? zhArray[Number(item)] : zhArray[Number(item)] + baseArray[index]
      return item
    })
    .reverse()
    .join('') // 倒叙回来数组，拼接成字符串
  string = string.replace(/^一十/, '十') // 如果以 一十 开头，可省略一 
  string = string.replace(/零+/, '零') // 如果有多位相邻的零，只写一个即可
  string = string[string.length - 1] === '零' ? string.slice(0, string.length - 1) : string
  return string
}
export const urlToBase64 = (url, size = false) => {
  return new Promise((resolve) => {
    let canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    let img = new Image()
    img.crossOrigin = 'Anonymous' // 解决Canvas.toDataURL 图片跨域问题
    img.src = url
    img.onload = function () {
      canvas.width = img.width
      canvas.height = img.height
      ctx.fillStyle = '#fff' // canvas背景填充颜色默认为黑色
      ctx.fillRect(0, 0, img.width, img.height)
      ctx.drawImage(img, 0, 0) // 参数可自定义
      const dataURL = canvas.toDataURL('image/png') // 获取Base64编码 
      resolve(size ? { width: img.width, height: img.height, dataURL } : dataURL)
      canvas = null // 清除canvas元素
      img = null // 清除img元素
    }
    img.onerror = () => {
      resolve('')
      // reject(new Error('Could not load image at ' + url))
    }
  })
}
export const initFont = () => {
  const baseURL = process.env.NODE_ENV === 'development' ? 'http://localhost:2000/' : process.env?.VUE_APP_CATALOG || '/'
  const url = `${baseURL}global/font/`
  let fontList = [
    {
      fontUrl: `${url}FZPHFW.TTF`,
      fontFamily: 'FZPHFW',
      label: 'FZPHFW'
    },
    {
      fontUrl: `${url}LCD2Bold.ttf`,
      fontFamily: 'LCD2',
      label: 'LCD2 Bold'
    },
    {
      fontUrl: `${url}YouSheBiaoTiHei.ttf`,
      fontFamily: 'YouSheBiaoTiHei',
      label: 'YouSheBiaoTiHei'
    },
    {
      fontUrl: `${url}zangwenxinbai.TTF`,
      fontFamily: 'zangwenxinbai',
      label: 'zangwenxinbai'
    }
  ]
  async function loadFonts (fontFamily, fontUrl) {
    const font = new FontFace(fontFamily, `url(${fontUrl})`)
    await font.load()
    document.fonts.add(font)
  }
  for (let i in fontList) {
    loadFonts(fontList[i].fontFamily, fontList[i].fontUrl)
  }
}
/**
 * @description: 设置系统字体
 * @param {String} family 字体
 */
export const change_font_family = family => {
  sessionStorage.setItem('data-family', family)
  window.document.documentElement.setAttribute('data-family', family)
}
export const get_font_family = () => sessionStorage.getItem('data-family') || 'cn'
/**
 * @description: 获取地图文件
 * @param {String} url 文件地址
 */
export const getMap = (areaLevel, areaId) =>
  new Promise((resolve, reject) => {
    const baseURL = process.env.NODE_ENV === 'development' ? 'http://localhost:2000/' : process.env?.VUE_APP_CATALOG || '/'
    const url = `${baseURL}map/${areaLevel}/${areaId}.json`
    axios
      .get(url)
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
export const isNewVersion = async () => {
  const baseURL = process.env.NODE_ENV === 'development' ? 'http://localhost:2000/' : process.env?.VUE_APP_CATALOG || '/'
  const url = `${baseURL}global/json/version.json?id=${new Date().getTime()}`
  const { data } = await axios.get(url)
  const version = localStorage.getItem('version') || ''
  if (version && version !== data.version) {
    console.log('版本更新')
    localStorage.setItem('version', data.version)
    window.location.reload(true)
    // window.location.reload(window.location.href)
  } else {
    localStorage.setItem('version', data.version)
  }
}
export const throttle = (func, delay) => {
  let timeout = null
  return function () {
    const context = this
    const args = arguments
    if (!timeout) {
      timeout = setTimeout(function () {
        timeout = null
        func.apply(context, args)
      }, delay)
    }
  }
}
export const formatDate = (timestamp, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!timestamp) return ''
  const date = new Date(timestamp)

  const formatObj = {
    YYYY: date.getFullYear(),
    MM: String(date.getMonth() + 1).padStart(2, '0'),
    DD: String(date.getDate()).padStart(2, '0'),
    HH: String(date.getHours()).padStart(2, '0'),
    mm: String(date.getMinutes()).padStart(2, '0'),
    ss: String(date.getSeconds()).padStart(2, '0')
  }

  return format.replace(/(YYYY|MM|DD|HH|mm|ss)/g, (match) => formatObj[match])
}
export const throttleIsNewVersion = throttle(isNewVersion, 1000)
