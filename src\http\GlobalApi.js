import HTTP, { baseURL } from './index'
import { saveAs } from 'file-saver'
import utils from '@/assets/js/utils.js'
const openImgEncrypt = (fileId) => btoa(encodeURIComponent(utils.encrypt(fileId, new Date().getTime(), '1')).replace(/%([0-9A-F]{2})/g, (match, p1) => {
  return String.fromCharCode('0x' + p1)
}))
const GlobalApi = {
  fileURL (id) {
    const token = sessionStorage.getItem('token') || ''
    let urlPath = ''
    if (token) {
      urlPath = id + (id.indexOf('?') !== -1 ? '&Authorization=' : '?Authorization=') + token
    } else {
      urlPath = 'SHARE_' + openImgEncrypt(id)
    }
    return `${baseURL}/image/${urlPath}`
  },
  openImgURL (id) {
    return `${baseURL}/image/SHARE_${openImgEncrypt(id)}`
  },
  defaultImgURL (id) {
    return `${baseURL}/img/${id}`
  },
  filePreview (id) {
    return `${baseURL}/file/preview/${id}`
  },
  globalFileInfo (id) {
    return HTTP.json(`/file/info/${id}`)
  },
  globalUpload (params, callback, id) {
    // 上传文件
    return HTTP.fileUpload('/file/upload', params, callback, id)
  },
  globalDownload (fileId, params) {
    // 下载文件
    if (sessionStorage.getItem('token')) {
      return HTTP.fileDownload(`/file/download/${fileId}`, params)
    } else {
      return HTTP.fileDownload(`/in_system/file/download/${fileId}`, params)
    }
  },
  dictionaryData (params) {
    // 获取字典
    return HTTP.json('/dictionary/selector', params)
  },
  openApiDictionaryData (params) {
    // 获取字典
    return HTTP.json('/open_api/dictionary/selector', params)
  },
  dictionaryNameData (params) {
    // 获取字典名称
    return HTTP.json('/dictionaryDefinition/selector', params)
  },
  inSystemGlobalDownload (fileId, params) {
    // 公开下载文件
    return HTTP.fileDownload(`/in_system/file/download/${fileId}`, params)
  },
  wordTopdf (params) {
    // word转pdf
    if (sessionStorage.getItem('token')) {
      return HTTP.fileUploadDownload('/file/word2pdf', params)
    } else {
      return HTTP.fileUploadDownload('/in_system/file/word2pdf', params)
    }
  },
  xlsToxlsx (params) {
    // xls转xlsx
    if (sessionStorage.getItem('token')) {
      return HTTP.fileUploadDownload('/file/xls2xlsx', params)
    } else {
      return HTTP.fileUploadDownload('/in_system/file/xls2xlsx', params)
    }
  },
  globalDocumentTemplate (id, code, callback) {
    // 通过编号获取文档模板
    return HTTP.fileDownload(`/documentTemplate/load/${code}`, {}, 'arraybuffer', {
      onDownloadProgress: progressEvent => {
        callback(progressEvent, id)
      }
    })
  },
  globalDocumentZip (url, params) {
    // 下载解析zip
    return HTTP.fileDownload(url, params, 'arraybuffer')
  },
  globalElectronFileDownload (id, fileId, params, callback) {
    return HTTP.fileDownload(`/file/download/${fileId}`, params, 'arraybuffer', {
      onDownloadProgress: progressEvent => {
        callback(progressEvent, id)
      }
    })
  },
  globalFileDownload (fileId, fileName, params) {
    // 下载文件
    if (sessionStorage.getItem('token')) {
      HTTP.fileDownload(`/file/download/${fileId}`, params).then(res => {
        saveAs(new Blob([res]), fileName)
      })
    } else {
      HTTP.fileDownload(`/in_system/file/download/${fileId}`, params).then(res => {
        saveAs(new Blob([res]), fileName)
      })
    }
  },
  globalProgressDownload (id, fileId, params, callback) {
    // 下载文件
    if (sessionStorage.getItem('token')) {
      return HTTP.fileDownload(`/file/download/${fileId}`, params, 'blob', {
        onDownloadProgress: progressEvent => {
          callback(progressEvent, id)
        }
      })
    } else {
      return HTTP.fileDownload(`/in_system/file/download/${fileId}`, params, 'blob', {
        onDownloadProgress: progressEvent => {
          callback(progressEvent, id)
        }
      })
    }
  },
  globalProgressDownloadZip (params, id, callback) {
    // 批量下载文件
    return HTTP.fileDownload('/file/zip', params, 'blob', {
      onDownloadProgress: progressEvent => {
        callback(progressEvent, id)
      }
    })
  },
  // 其他接口文件下载
  globalOtherDownload (url, params, id, callback, config = {}) {
    // 下载文件
    return HTTP.fileDownload(url, params, 'blob', {
      ...config,
      onDownloadProgress: progressEvent => {
        callback(progressEvent, id)
      }
    })
  },
  globalGet (url, params) {
    return HTTP.get(url, params)
  },
  globalPost (url, params) {
    return HTTP.post(url, params)
  },
  globalJson (url, params) {
    return HTTP.json(url, params)
  },
  templateInfo (params) {
    // 业务编号获取模板
    return HTTP.json('/themeTemplate/business/info', params)
  },
  globalData (params) {
    // 通用列表查询免登录获取数据
    return HTTP.json('/wind/runner/openList', params)
  },
  globalReadConfig (params) {
    // 通用取配置接口
    return HTTP.json('/config/read', params, { terminal: 'PC' })
  },
  globalReadOpenConfig (params) {
    // 通用公开取配置接口
    return HTTP.json('/config/openRead', params)
  },
  globalExportExcelHead (module) {
    // 通用导出表头
    return HTTP.json(`/excel/export/properties/${module}`)
  },
  globalExportExcelData (module, params) {
    // 通用导出数据
    return HTTP.json(`/excel/export/data/${module}`, params)
  },
  globalExcelImport (url, params, config) {
    // 通用Excel导入数据
    return HTTP.fileUpload(url, params, () => { }, '', config)
  },
  authorize (params) {
    return `${baseURL}/oauth/authorize?response_type=code&scope=all&${params}`
  },
  login (params, config = {}) {
    // 登录
    return HTTP.post('/oauth/token', params, { terminal: 'PC', ...config })
  },
  oauthToken (params) {
    // 游客登陆
    return HTTP.post('/oauth/token', params)
  },
  appToken (params) {
    // 登录
    return HTTP.post('/scanCodeLogin/gain/appToken', params, { terminal: 'PC' })
  },
  verifyLoginCode (params) {
    return HTTP.json('/open_api/verifyCode/enableStatus', params)
  },
  licenceInfo (params) {
    return HTTP.get('/licence/info', params)
  },
  licenceUpdate (params) {
    return HTTP.get('/licence/update', params)
  },
  loginUser (params) {
    // 登录获取用户信息
    return HTTP.get('/login/user', params, { terminal: 'PC' })
  },
  verifyLoginUser (params, areaId) {
    // 登录获取用户信息
    return HTTP.get('/login/user', params, { areaId: areaId })
  },
  loginMenu (params) {
    // 登录获取菜单信息
    return HTTP.get('/login/menus', params, { terminal: 'PC' })
  },
  loginRole (params) {
    // 登录获取角色信息
    return HTTP.get('/login/roles', params, { terminal: 'PC' })
  },
  loginAreas (params) {
    // 获取地区
    return HTTP.json('/login/areas', params, { terminal: 'PC' })
  },
  loginOut (params) {
    // 获取地区
    return HTTP.json('/login/out', params, { terminal: 'PC' })
  },
  longShortLink (url, params) {
    // 长链接转短链接
    if (sessionStorage.getItem('token')) {
      return HTTP.get(`/longShortLink/exchange?longUrl=${url}`, params)
    } else {
      return HTTP.get(`/open_api/longShortLink/exchange?longUrl=${url}`, params)
    }
  }
}
export default GlobalApi
