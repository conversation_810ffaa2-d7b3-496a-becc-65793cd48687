<template>
  <div class="CommitteeLivingRoomList">
    <van-sticky>
      <van-tabs v-if="switchs.data.length" v-model:active="switchs.value" :color="'#4488EB '" @click-tab="onClickTab">
        <van-tab :title="item.label" :name="item.value" v-for="item in switchs.data" :key="item.value"></van-tab>
      </van-tabs>
      <van-search v-model="keyword" shape="round" placeholder="请输入关键词" @search="onSearch"></van-search>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" :finished-text="dataList.length == 0 ? '' : '没有更多了'"
        offset="52" @load="onLoad">
        <div v-for="item in dataList" :key="item.id" class="list_box" @click="openDetails(item)">
          <div class="list_item">
            <div class="list_item_top">
              <div class="list_item_top_status">
                <img class="list_item_top_img"
                  :src="switchs.value == '2' || switchs.value == '1' ? require('@/assets/img/CommitteeLivingRoom-on.png') : require('@/assets/img/CommitteeLivingRoom-off.png')"
                  alt="">
                <span class="list_item_top_status_value"
                  :style="`color:${switchs.value == '3' ? '#999999' : '#3088fe'}`">
                  {{ switchs.value == '2' ? '当前' : switchs.value == '1' ? '预热' : switchs.value == '3' ? '已结束' :
                    '' }}</span>
              </div>
              <div class="list_item_top_time">{{ item.scheduleDate }}</div>
            </div>
            <div class="list_item_info_box">
              <img src="@/assets/img/headImg.png" alt="" class="list_item_info_headimg">
              <div class="list_item_info">
                <div class="list_item_info_user">
                  <div v-if="item.userVo.userName" class="list_item_userName">{{ item.userVo?.userName }}</div>
                  <div v-if="item.userVo.sectorType" class="list_item_sectorType">{{ item.userVo?.sectorType?.name }}
                  </div>
                </div>
                <div class="list_item_position">{{ item.userVo?.position }}</div>
              </div>
            </div>
            <div class="list_item_title">{{ item.title }}</div>
            <div class="list_item_tag_msg_forward">
              <div class="list_item_tag">{{ item.scheduleType }}</div>
              <div class="list_item_msg_forward">
                <div class="list_item_msg">
                  <span class="list_item_msg_label">留言</span>
                  <span class="list_item_msg_value">{{ item.commentCount }}</span>
                </div>
                <div class="list_item_forward">
                  <span class="list_item_forward_label">转发</span>
                  <span class="list_item_forward_value">{{ item.shareCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <van-empty v-if="dataList.length == 0 && !loading" description="暂无数据" />
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
export default { name: 'CommitteeLivingRoomList' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const title = ref('委员会客厅列表')
const switchs = ref({ value: '2', data: [{ label: '当前', value: '2' }, { label: '预热', value: '1' }, { label: '往期', value: '3' }] })
const keyword = ref('')//搜索关键词
const loading = ref(false)//加载中
const finished = ref(false)//是否加载完成
const refreshing = ref(false)//下拉刷新
const pageNo = ref(1)//当前页码
const pageSize = ref(15)//每页条数
const total = ref(0)//总条数
const dataList = ref([])
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  setTimeout(() => {
    onRefresh()
  }, 100)
})
const onSearch = () => {
  onRefresh()
}
const onRefresh = () => {
  pageNo.value = 1
  dataList.value = []
  finished.value = false
  loading.value = true
  getList()
}
const onLoad = () => {
  if (dataList.value.length < total.value) {
    pageNo.value++
    getList()
  } else {
    finished.value = true
  }
}
// 获取委员会客厅列表
const getList = async () => {
  const params = {
    scheduleStatus: switchs.value.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    keyword: keyword.value,
    businessCode: 'schedule_reception'
  }
  const { data, code, total: totals } = await api.officeOnlineTopicList(params)
  if (code === 200) {
    dataList.value = dataList.value.concat(data || [])
    total.value = totals
    loading.value = false
    refreshing.value = false
    if (dataList.value.length >= total.value) {
      finished.value = true
    }
  } else {
    loading.value = false
    finished.value = true
    refreshing.value = false
  }
}
// 切换tab
const onClickTab = () => {
  onRefresh()
}
// 跳转委员会客厅详情
const openDetails = (item) => {
  router.push({ path: '/CommitteeLivingRoomDetails', query: { id: item.id, receiverId: item.userId } })
}
</script>
<style lang="scss">
.CommitteeLivingRoomList {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #F4F5F9;

  .list_box {
    // padding: 10px 12px;
    background-color: #fff;
    border-top: 1px solid #F4F4F4;
    margin: 12px 16px;
    box-shadow: rgba(24, 64, 118, 0.08) 0px 2px 10px 1px;
    border-radius: 4px;

    .list_item {
      position: relative;

      .list_item_top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 10px;

        .list_item_top_status {
          display: flex;
          align-items: center;

          .list_item_top_img {
            width: 20px;
            height: 20px;
          }

          .list_item_top_status_value {
            font-weight: 400;
            font-size: 14px;
          }
        }

        .list_item_top_time {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
        }
      }

      .list_item_info_box {
        padding: 20px 20px;
        background: #f4f5f7;
        display: flex;
        align-items: flex-start;

        .list_item_info_headimg {
          width: 46px;
          height: 46px;
          border-radius: 50%;
        }

        .list_item_info {
          margin-left: 10px;

          .list_item_info_user {
            display: flex;
            align-items: center;
            margin-bottom: 5px;

            .list_item_userName {
              font-size: 16px;
              font-weight: 600;
              color: #333333;
              margin-right: 10px;
            }

            .list_item_sectorType {
              font-size: 14px;
              font-weight: 400;
              color: #999999;
            }
          }

          .list_item_position {
            font-size: 14px;
            font-weight: 400;
            color: #666666;
          }
        }
      }

      .list_item_title {
        font-size: 17px;
        font-weight: 600;
        color: #333333;
        padding: 10px;
      }

      .list_item_tag_msg_forward {
        margin-top: 6px;
        padding: 0 10px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .list_item_tag {
          background-color: #fdf7eb;
          font-weight: 400;
          color: #f6931c;
          padding: 3px 10px;
          font-size: 14px;
        }

        .list_item_msg_forward {
          display: flex;
          align-items: center;

          .list_item_msg {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            margin-left: 40px;

            .list_item_msg_value {
              color: #3088fe;
              margin-left: 4px;
            }
          }

          .list_item_forward {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            margin-left: 40px;

            .list_item_forward_value {
              color: #3088fe;
              margin-left: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
