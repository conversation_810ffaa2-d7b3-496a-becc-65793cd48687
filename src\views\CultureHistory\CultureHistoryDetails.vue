<template>
  <div class="NegotiationTopicsDetails">
    <div class="ndd-title">{{ info.infoTitle }}</div>
    <div class="ndd-meta">
      <span class="ndd-type">{{ formatDate(info.pubTime, 'YYYY-MM-DD') }}</span>
      <span class="ndd-status">{{ info.infoSource }}</span>
    </div>
    <div class="ndd-detail">
      <div class="ndd-content" @click="setImgBigger" v-html="info.infoContent"></div>
    </div>
    <!-- <div v-if="info.attachments && info.attachments.length" class="ndd-attachment">
      <div @click="getAttachmentUrl(info.attachments[0])">
        <img src="@/assets/img/fileicon/icon_word.png" alt="" style="width: 22px;height: 22px;margin-right: 10px;">
        <span> {{ info.attachments[0].originalFileName }} </span>
      </div>
    </div> -->
    <!-- 评论区 -->
    <div v-if="false" class="ndd-comments">
      <div class="ndd-comments-title">
        <span class="ndd-comments-title-bar"></span>
        全部评论
      </div>
      <div class="ndd-comment-list">
        <template v-if="commentList && commentList.length > 0">
          <div class="ndd-comment-item" v-for="(item, index) in commentList" :key="index">
            <img src="@/assets/img/headImg.png" alt="" class="ndd-comment-item-head-Img">
            <div style="width: 100%;">
              <div class="ndd-comment-item-top">
                <div class="ndd-comment-item-user-tag">
                  <span class="ndd-comment-item-user">{{ item.commentUserName }}</span>
                  <div class="ndd-comment-item-tag-box">
                    <span class="ndd-comment-item-tag">{{ item.userArea }}</span>
                  </div>
                </div>
                <span class="ndd-comment-status" v-if="item.checkedStatus != 1">{{ item.checkedStatus != '2' ? '审核中' :
                  '未通过'
                  }}</span>
                <div class="ndd-comment-list-like" v-if="item.checkedStatus == 1" @click="handleCommentLikes(item)">
                  <span>{{ item.praisesCount }}</span>
                  <img class="ndd-comment-list-like-img"
                    :src="item.hasClickPraises ? require('@/assets/img/fabulous_o.png') : require('@/assets/img/fabulous.png')"
                    alt="">
                </div>
              </div>
              <div class="ndd-comment-content">{{ item.commentContent }}</div>
              <div class="ndd-comment-img" v-if="item.fileInfos && item.fileInfos.length">
                <img v-for="(img, imgIdx) in item.fileInfos" :key="img.id" :src="getCommentImgUrl(img)"
                  :alt="img.originalFileName" class="ndd-comment-img-item"
                  @click="previewCommentImages(item.fileInfos, imgIdx)" />
              </div>
              <div class="ndd-comment-footer">
                <span class="ndd-comment-time">{{ formatDate(item.createDate, 'YYYY-MM-DD HH:mm') }}</span>
                <span class="ndd-comment-delete" @click="handle(item)">{{ item.createBy == user.id ? '删除' : '回复'
                  }}</span>
              </div>
              <!-- 嵌套回复 -->
              <div v-if="item.children && item.children.length" class="ndd-comment-reply-list">
                <div class="ndd-comment-reply-item" v-for="reply in item.children" :key="reply.id">
                  <img src="@/assets/img/headImg.png" alt="" class="ndd-comment-reply-head-img">
                  <div>
                    <div>
                      <span class="ndd-comment-reply-user">{{ reply.commentUserName }}</span>
                      <span class="ndd-comment-reply-to">回复</span>
                      <span class="ndd-comment-reply-target">{{ reply.toCommenter }}</span>
                    </div>
                    <div class="ndd-comment-reply-content">{{ reply.commentContent }}</div>
                    <span class="ndd-comment-reply-time">{{ formatDate(reply.createDate, 'YYYY-MM-DD HH:mm') }}</span>
                    <span class="ndd-comment-delete" @click="handle(reply)">{{ reply.createBy == user.id ? '删除' : '回复'
                      }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="ndd-comments-more">暂无数据</div>
        </template>
      </div>
    </div>
    <!-- 发表评论 -->
    <div v-if="false" class="ndd-comment-input-bar">
      <input class="ndd-comment-input" :placeholder="isCollectionEnded() ? '征集已结束' : '发表评论'" v-model="commentInput"
        readonly @click="handleCommentClick" />
      <div style="display: flex;align-items: center;">
        <span class="ndd-comment-count">{{ commentCount }}</span>
        <img src="@/assets/img/comment.png" alt="" style="width: 20px;height: 20px;margin-left: 8px;">
      </div>
      <div style="display: flex;align-items: center;margin-left: 15px;" @click="handleLikes()">
        <span class="ndd-like-count">{{ likesCount }}</span>
        <img :src="hasClickPraises ? require('@/assets/img/fabulous_o.png') : require('@/assets/img/fabulous.png')"
          alt="" style="width: 20px;height: 20px;margin-left: 8px;">
      </div>
    </div>
    <!-- 评论弹窗 -->
    <van-popup v-model:show="showCommentPopup" position="bottom"
      :style="{ height: commentImages.length > 0 ? '280px' : '230px', borderRadius: '16px 16px 0 0' }">
      <div class="comment-popup-content">
        <van-field v-model="commentInput" rows="4" autosize type="textarea" maxlength="300"
          :placeholder="replyToUser ? `回复${replyToUser}` : '请输入评论'" show-word-limit />
        <!-- 已上传图片预览 -->
        <div class="comment-upload-preview" v-if="commentImages.length">
          <div class="comment-upload-preview-item" v-for="(img, idx) in commentImages" :key="img.id"
            @click.stop="previewCommentImages(commentImages, idx)">
            <img :src="getCommentImgUrl(img)" class="comment-upload-preview-img" />
            <span class="comment-upload-preview-remove" @click.stop="removeCommentImage(idx)">×</span>
          </div>
        </div>
        <div class="comment-popup-bottom">
          <div style="flex: 1; display: flex; align-items: center;">
            <input ref="fileInput" type="file" accept="image/*" style="display: none" @change="handleImageUpload"
              multiple />
            <img src="@/assets/img/icon_upload_img.png" alt="上传图片" class="comment-upload-icon"
              @click="triggerFileInput" />
          </div>
          <div class="comment-popup-actions">
            <span style="color:#888;font-size:14px;">{{ commentInput.length }}/300</span>
            <van-button type="primary" size="small" @click="sendComment" :disabled="!commentInput.trim()"
              style="margin-left: 12px;">发送</van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
export default { name: 'NegotiationTopicsDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { formatDate } from '@/assets/js/utils.js'
import { showToast, showConfirmDialog, showImagePreview } from 'vant'
import config from '@/config'
// Vant 组件库样式
import 'vant/lib/index.css'

const route = useRoute()
const title = ref('文史资料详情')
const info = ref({})
const commentList = ref([])
const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
const showCommentPopup = ref(false)
const commentInput = ref('')
const commentCount = ref(0)
const likesCount = ref(0)
const hasClickPraises = ref(false)
// 新增：回复相关变量
const replyToUser = ref('')
const replyParentId = ref(null)
const commentImages = ref([]) // 存储已上传图片信息
const fileInput = ref(null)

onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  getInfo()
})
// 获取详情
const getInfo = async () => {
  const { data } = await api.newsContentInfo({ detailId: route.query.id })
  info.value = data
  getCommentList()
}
// 获取评论列表
const getCommentList = async () => {
  const { data } = await api.commentList({
    businessCode: "informationContent",
    businessId: route.query.id,
    pageNo: 1,
    pageSize: 15,
    query: {}
  })
  commentList.value = data
  getCount()
}
// 获取评论、点赞数量
const getCount = async () => {
  const { data } = await api.getCount({
    businessCode: "informationContent",
    businessId: route.query.id,
    isExcludeSubComment: 1
  })
  commentCount.value = data.commentCount
  likesCount.value = data.praisesCount
  hasClickPraises.value = data.hasClickPraises
}
// 网络议政的点赞和取消点赞
const handleLikes = () => {
  if (hasClickPraises.value) {
    hasClickPraises.value = false
    likesCount.value--
    getLikeDel()
  } else {
    hasClickPraises.value = true
    likesCount.value++
    getLikeAdd()
  }
}
// 网络议政取消点赞请求
const getLikeDel = async (_type, _id) => {
  await api.getLikeDel({
    businessCode: _type || "informationContent",
    businessId: _id || route.query.id
  })
}
// 网络议政点赞请求
const getLikeAdd = async (_type, _id) => {
  await api.getLikeAdd({
    form: {
      businessCode: _type || "informationContent",
      businessId: _id || route.query.id
    }
  })
}
// 处理网络议政评论的删除和回复
const handle = (_item) => {
  if (_item.createBy == user.value.id) {
    handleDels(_item)
  } else {
    handleReply(_item)
  }
}
// 网络议政评论删除
const handleDels = (_item) => {
  showConfirmDialog({
    title: '提示',
    confirmButtonColor: '#4488EB',
    message: '确定删除所选的评论吗？'
  }).then(async () => {
    const res = await api.commentDels({ ids: [_item.id] })
    console.log('删除评论', res)
    if (res.code == 200) {
      showToast('删除成功')
      getCommentList()
    } else {
      showToast(res.data || res.message)
    }
  }).catch(() => {
  })
}
// 网络议政评论回复
const handleReply = (_item) => {
  // 检查征集是否已结束
  if (isCollectionEnded()) {
    showToast('征集已结束，无法回复评论')
    return
  }
  replyToUser.value = _item.commentUserName
  replyParentId.value = _item.id
  showCommentPopup.value = true
  commentInput.value = ''
}
// 网络议政评论的点赞、取消点赞
const handleCommentLikes = (_item) => {
  if (_item.hasClickPraises) {
    _item.hasClickPraises = false
    _item.praisesCount--
    getLikeDel('comment', _item.id)
  } else {
    _item.hasClickPraises = true
    _item.praisesCount++
    getLikeAdd('comment', _item.id)
  }
}
// 附件下载地址
// const getAttachmentUrl = async (file) => {
//   api.globalFileDownload(file.id, file.originalFileName)
//   console.log('file===>', file)
// }

// 处理评论点击事件
const handleCommentClick = () => {
  // 检查征集是否已结束
  if (isCollectionEnded()) {
    showToast('征集已结束，无法发表评论')
    return
  }
  showCommentPopup.value = true
}
// 判断征集是否已结束
const isCollectionEnded = () => {
  if (!info.value.endDate) {
    return false
  }
  const endDate = new Date(info.value.endDate)
  const currentDate = new Date()
  return currentDate > endDate
}
const setImgBigger = (e) => {
  if (e.target.nodeName === 'IMG') {
    var taga = document.querySelectorAll('.ndd-content img') // 返回一个标签对象数组
    var img = []
    var nowIndex = 0
    taga.forEach((element, index) => {
      if (element.src === e.target.currentSrc) {
        nowIndex = index
      }
      img.push(element.src)
    })
    showImagePreview({ images: img, startPosition: nowIndex, closeable: true })
  }
}
const handleImageUpload = async (event) => {
  const files = event.target.files
  if (!files.length) return
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const formData = new FormData()
    formData.append('file', file)
    const res = await api.globalUpload(formData, () => { })
    if (res && res.data) {
      commentImages.value.push(res.data)
    }
  }
  event.target.value = '' // 允许重复上传同一图片
}
const removeCommentImage = (idx) => {
  commentImages.value.splice(idx, 1)
}
// 发送评论/回复
const sendComment = async () => {
  const attachmentIds = commentImages.value.map(img => img.id).join(',')
  const res = await api.commentAdd({
    form: {
      attachmentIds,
      businessCode: 'informationContent',
      businessId: route.query.id,
      commentContent: commentInput.value,
      terminalName: 'APP',
      parentId: replyParentId.value || null
    }
  })
  if (res.code == 200) {
    showToast('发送成功')
    getCommentList()
  } else {
    showToast(res.data || res.message)
  }
  showCommentPopup.value = false
  commentInput.value = ''
  replyToUser.value = ''
  replyParentId.value = null
  commentImages.value = [] // 清空已选图片
}
const getCommentImgUrl = (img) => {
  return config.API_URL + '/image/' + img.newFileName
}
const previewCommentImages = (fileInfos, startPosition = 0) => {
  const images = fileInfos.map(img => getCommentImgUrl(img));
  showImagePreview({
    images,
    startPosition
  })
}
const triggerFileInput = () => {
  fileInput.value && fileInput.value.click()
}
</script>
<style lang="scss">
.NegotiationTopicsDetails {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fff;

  .ndd-title {
    font-size: 20px;
    font-weight: bold;
    margin: 16px;
  }

  .ndd-meta {
    margin: 0 16px;
    color: #888;
    font-size: 14px;
    display: flex;
    align-items: center;

    .ndd-type {
      font-size: 14px;
      color: rgb(102, 102, 102);
      margin-right: 10px;
    }

    .ndd-status {
      color: rgb(102, 102, 102);
    }
  }

  .ndd-detail {
    margin: 10px 16px;

    .ndd-row {
      margin-bottom: 10px;
      color: rgb(51, 51, 51);
      font-size: 14px;
    }

    .ndd-label {
      color: rgb(51, 51, 51);
      font-weight: 600;
      font-size: 14px;
    }

    .ndd-content {
      width: 100%;
      font-size: 16px;
      line-height: 28px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .ndd-attachment {
    margin: 16px 16px;
    padding: 2px 10px;
    border: 1px solid #F4F5F7;
    min-height: 36px;
    border-radius: 4px 4px 4px 4px;
    display: flex;
    align-items: center;
  }

  .ndd-comments {
    border-top: 10px solid rgb(248, 248, 248);
    background: #fff;
    padding: 16px;

    .ndd-comments-title {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 16px;

      .ndd-comments-title-bar {
        display: inline-block;
        width: 4px;
        height: 18px;
        background: #2267d8;
        border-radius: 2px;
        margin-right: 8px;
      }
    }

    .ndd-comment-list {
      display: flex;
      flex-direction: column;
      padding-bottom: 30px;
      margin-bottom: 16px;

      .ndd-comment-item {
        display: flex;
        align-items: flex-start;
        width: auto;
        padding: 15px 0px;
        border-bottom: 1px solid rgb(238, 238, 238);

        .ndd-comment-item-head-Img {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          margin-right: 8px;
          margin-top: 2px;
        }

        .ndd-comment-item-top {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .ndd-comment-item-user-tag {
            display: flex;
            align-items: center;

            .ndd-comment-item-user {
              font-weight: 600;
              color: #333333;
              font-size: 16px;
              margin-right: 15px;
            }

            .ndd-comment-item-tag-box {
              background: rgba(102, 102, 102, 0.1);
              padding: 0 10px;
              border-radius: 20px;
              align-items: center;
              justify-content: center;

              .ndd-comment-item-tag {
                font-size: 14px;
                color: rgb(102, 102, 102);
              }
            }
          }

          .ndd-comment-status {
            font-size: 14px;
            color: rgb(230, 162, 60);
          }

          .ndd-comment-list-like {
            display: flex;
            align-items: center;

            span {
              margin-right: 5px;
              color: rgb(102, 102, 102);
              font-size: 14px;
            }

            .ndd-comment-list-like-img {
              width: 19px;
              height: 19px;
            }
          }
        }

        .ndd-comment-content {
          color: #333333;
          margin-top: 5px;
          word-break: break-all;
          font-size: 15px;
        }

        .ndd-comment-img {
          margin-top: 15px;

          .ndd-comment-img-item {
            width: 60px;
            height: 60px;
            margin-right: 10px;
            object-fit: cover;
            border-radius: 4px;
          }
        }

        .ndd-comment-footer {
          display: flex;
          align-items: center;
          margin-top: 10px;

          .ndd-comment-time {
            font-size: 14px;
            color: rgb(153, 153, 153);
          }

          .ndd-comment-delete {
            font-size: 14px;
            margin-left: 20px;
            color: rgb(51, 51, 51);
          }
        }
      }
    }

    .ndd-comments-more {
      text-align: center;
      color: #bbb;
      font-size: 13px;
      margin-top: 8px;
    }
  }

  .ndd-comment-input-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-top: 1px solid #eee;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    z-index: 10;

    .ndd-comment-input {
      flex: 1;
      border: none;
      outline: none;
      background: #f4f5f9;
      border-radius: 16px;
      padding: 8px 12px;
      margin-right: 8px;
      font-size: 14px;
    }

    .ndd-comment-send {
      background: #409EFF;
      color: #fff;
      border: none;
      border-radius: 16px;
      padding: 6px 16px;
      cursor: pointer;
      margin-right: 8px;
      font-size: 14px;
    }

    .ndd-comment-count {
      color: #888;
      margin-left: 8px;
      font-size: 14px;
    }

    .ndd-like-count {
      color: #888;
      margin-left: 8px;
      font-size: 14px;
    }
  }
}

.ndd-comment-reply-list {
  background: #f7f8fa;
  border-radius: 8px;
  margin: 10px 0 0 0;
  padding: 10px 15px;
}

.ndd-comment-reply-item {
  font-size: 14px;
  color: #333;
  margin-top: 8px;
  display: flex;
  align-items: flex-start;

  .ndd-comment-reply-head-img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .ndd-comment-reply-user {
    font-weight: bold;
    margin-right: 4px;
    font-size: 15px;
  }

  .ndd-comment-reply-to {
    color: #888;
    margin: 0 4px;
    font-size: 15px;
  }

  .ndd-comment-reply-target {
    font-weight: bold;
    margin-right: 4px;
    font-size: 15px;
  }

  .ndd-comment-reply-content {
    color: #333;
    font-size: 14px;
    margin: 6px 0;
  }

  .ndd-comment-reply-time {
    color: #bbb;
    font-size: 13px;
  }

  .ndd-comment-delete {
    color: #000;
    font-size: 13px;
    margin-left: 10px;
  }
}

.comment-popup-content {
  padding: 16px 16px 0 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;

  .comment-popup-bottom {
    display: flex;
    align-items: center;
    margin-top: 12px;
    justify-content: space-between;

    .comment-upload-icon {
      width: 24px;
      height: 24px;
    }

    .comment-popup-actions {
      display: flex;
      align-items: center;
    }
  }

  .comment-upload-preview {
    display: flex;
    margin: 8px 0 0 0;

    .comment-upload-preview-item {
      position: relative;
      margin-right: 8px;

      .comment-upload-preview-img {
        width: 48px;
        height: 48px;
        object-fit: cover;
        border-radius: 4px;
      }

      .comment-upload-preview-remove {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #fff;
        color: #f00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        text-align: center;
        line-height: 18px;
        font-size: 14px;
        cursor: pointer;
        border: 1px solid #eee;
      }
    }
  }
}
</style>
