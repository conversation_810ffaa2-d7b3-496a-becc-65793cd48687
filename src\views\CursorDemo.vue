<template>
  <div class="container">
    <!-- 顶部标题和按钮 -->
    <div class="header">
      <div class="title-box">
        <div class="title">协商议题预热</div>
        <div class="title-underline"></div>
      </div>
      <button class="all-btn">全部</button>
    </div>

    <!-- 顶部图片，支持跳转 -->
    <div class="banner-box" @click="goToBanner">
      <img class="banner-img" src="/src/assets/img/icon_activity_preheat.png" alt="banner" />
      <div class="banner-label">协商活动预热</div>
    </div>

    <!-- 协商议题 -->
    <div class="topics-section">
      <div class="topics-title">协商议题</div>
      <div class="topics-list">
        <div class="topic-left" v-if="topics[0]">
          <div class="topic-icon">
            <img :src="topics[0].icon" alt="icon" />
          </div>
          <div class="topic-content">
            <div class="topic-title">{{ topics[0].title }}</div>
            <button class="topic-btn" @click="goToTopic(topics[0])">去看看</button>
          </div>
        </div>
        <div class="topic-right">
          <div class="topic-item" v-for="(item) in topics.slice(1, 3)" :key="item.id">
            <div class="topic-icon-small">
              <img :src="item.icon" alt="icon" />
            </div>
            <div class="topic-title-small">{{ item.title }}</div>
            <button class="topic-btn-small" @click="goToTopic(item)">去看看</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 提交意见建议按钮 -->
    <button class="submit-btn" @click="goToSubmit">提交意见建议</button>

    <!-- 底部两个操作按钮 -->
    <div class="bottom-actions">
      <button class="action-btn" @click="goToNetworkDiscuss">参加网络议政</button>
      <button class="action-btn" @click="goToQuestionnaire">参加问卷调查</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CursorDemo',
  data () {
    return {
      topics: [
        {
          id: 1,
          title: '关于“智慧西安议政云公...”，',
          icon: require('@/assets/img/icon_committee_living_room.png'),
        },
        {
          id: 2,
          title: '关于“推进5G网络建设和应用”议题',
          icon: require('@/assets/img/icon_network.png'),
        },
        {
          id: 3,
          title: '关于“优化营商环境”议题',
          icon: require('@/assets/img/icon_department.png'),
        },
      ],
    };
  },
  methods: {
    // 预留：获取议题接口
    fetchTopics () {
      // TODO: 调用接口获取议题数据
    },
    goToBanner () {
      // TODO: 跳转到活动预热页面
      this.$router.push({ name: 'ActivityPreheat' });
    },
    goToTopic (topic) {
      // TODO: 跳转到议题详情
      this.$router.push({ name: 'TopicDetail', params: { id: topic.id } });
    },
    goToSubmit () {
      // TODO: 跳转到提交意见建议页面
      this.$router.push({ name: 'SubmitAdvice' });
    },
    goToNetworkDiscuss () {
      // TODO: 跳转到网络议政页面
      this.$router.push({ name: 'NetworkDiscuss' });
    },
    goToQuestionnaire () {
      // TODO: 跳转到问卷调查页面
      this.$router.push({ name: 'Questionnaire' });
    },
  },
};
</script>

<style scoped>
.container {
  background: #f7faff;
  min-height: 100vh;
  padding: 0 16px 16px 16px;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 16px;
}

.title-box {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #222;
}

.title-underline {
  width: 32px;
  height: 4px;
  background: #3b7cff;
  border-radius: 2px;
  margin-top: 4px;
}

.all-btn {
  background: #eaf2ff;
  color: #3b7cff;
  border: none;
  border-radius: 16px;
  padding: 4px 16px;
  font-size: 14px;
  cursor: pointer;
}

.banner-box {
  position: relative;
  margin: 20px 0 16px 0;
  cursor: pointer;
}

.banner-img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(59, 124, 255, 0.08);
}

.banner-label {
  position: absolute;
  left: 16px;
  top: 16px;
  background: linear-gradient(90deg, #ff6b3b 0%, #ff3b3b 100%);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  padding: 4px 16px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(255, 59, 59, 0.08);
}

.topics-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(59, 124, 255, 0.04);
}

.topics-title {
  font-size: 16px;
  font-weight: bold;
  color: #3b7cff;
  margin-bottom: 12px;
}

.topics-list {
  display: flex;
  gap: 12px;
}

.topic-left {
  flex: 1.2;
  background: #eaf2ff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 12px;
  gap: 12px;
}

.topic-icon img {
  width: 48px;
  height: 48px;
}

.topic-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.topic-title {
  font-size: 15px;
  color: #222;
  font-weight: 500;
  margin-bottom: 8px;
}

.topic-btn {
  background: linear-gradient(90deg, #3b7cff 0%, #3bb2ff 100%);
  color: #fff;
  border: none;
  border-radius: 16px;
  padding: 4px 16px;
  font-size: 14px;
  cursor: pointer;
}

.topic-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.topic-item {
  background: #f7faff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
}

.topic-icon-small img {
  width: 32px;
  height: 32px;
}

.topic-title-small {
  font-size: 14px;
  color: #222;
  flex: 1;
}

.topic-btn-small {
  background: linear-gradient(90deg, #3b7cff 0%, #3bb2ff 100%);
  color: #fff;
  border: none;
  border-radius: 16px;
  padding: 2px 12px;
  font-size: 13px;
  cursor: pointer;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(90deg, #3b7cff 0%, #3bb2ff 100%);
  color: #fff;
  border: none;
  border-radius: 24px;
  padding: 12px 0;
  font-size: 16px;
  font-weight: bold;
  margin: 16px 0;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(59, 124, 255, 0.08);
}

.bottom-actions {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.action-btn {
  flex: 1;
  background: #eaf2ff;
  color: #3b7cff;
  border: none;
  border-radius: 16px;
  padding: 12px 0;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(59, 124, 255, 0.04);
}
</style>
