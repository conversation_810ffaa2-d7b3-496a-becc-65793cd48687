<template>
  <div class="NoticeList">
    <van-sticky>
      <van-search v-model="keyword" shape="round" placeholder="请输入关键词" @search="onSearch"></van-search>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" :finished-text="dataList.length == 0 ? '' : '没有更多了'"
        offset="52" @load="onLoad">
        <div v-for="item in dataList" :key="item.id" class="list_box" @click="openDetails(item)">
          <div class="list_item">
            <div style="margin-left: 10px;">
              <div class="list_item_title">{{ item.theme }}</div>
              <div>
                <span class="list_item_time">{{ formatDate(item.publishTime, 'YYYY-MM-DD') }}</span>
                <span class="list_item_source">{{ item.channelId }}</span>
              </div>
            </div>
          </div>
        </div>
        <van-empty v-if="dataList.length == 0 && !loading" description="暂无数据" />
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
export default { name: 'NoticeList' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { formatDate } from '@/assets/js/utils.js'
import { useRouter } from 'vue-router'
const router = useRouter()

const title = ref('文史资料征集公告')
const keyword = ref('')//搜索关键词
const loading = ref(false)//加载中
const finished = ref(false)//是否加载完成
const refreshing = ref(false)//下拉刷新
const pageNo = ref(1)//当前页码
const pageSize = ref(15)//每页条数
const total = ref(0)//总条数
const dataList = ref([])
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  setTimeout(() => {
    onRefresh()
  }, 100)
})
const onSearch = () => {
  onRefresh()
}
const onRefresh = () => {
  pageNo.value = 1
  dataList.value = []
  finished.value = false
  loading.value = true
  getList()
}
const onLoad = () => {
  if (dataList.value.length < total.value) {
    pageNo.value++
    getList()
  } else {
    finished.value = true
  }
}
// 获取通知公告
const getList = async () => {
  const params = {
    keyword: keyword.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    query: {
      channelId: '1935275534502072322',
      isDraft: 0
    },
    tableId: 'id_message_notification',
    wheres: []
  }
  const { data, code, total: totals } = await api.notificationList(params)
  if (code === 200) {
    dataList.value = dataList.value.concat(data || [])
    total.value = totals
    loading.value = false
    refreshing.value = false
    if (dataList.value.length >= total.value) {
      finished.value = true
    }
  } else {
    loading.value = false
    finished.value = true
    refreshing.value = false
  }
}
// 跳转公告详情
const openDetails = (_item) => {
  router.push({ path: '/NoticeDetails', query: { id: _item.id } })
}
</script>
<style lang="scss">
.NoticeList {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #F4F5F9;

  .list_box {
    padding: 10px 12px;
    background-color: #fff;
    border-top: 1px solid #F4F4F4;

    .list_item {
      display: flex;
      align-items: flex-start;

      .list_item_title {
        font-size: 16px;
        margin: 5px 0px;
        font-weight: 600;
        color: #000;
      }

      .list_item_time {
        color: #999;
        font-size: 12px;
        margin-right: 10px;
      }

      .list_item_source {
        color: #999;
        font-size: 12px;
      }
    }
  }

}
</style>
