<template>
  <div class="QuestionnaireDetails">
    <div class="qd-title">{{ paperInfo.name || paperInfo.title }}</div>
    <div class="qd-desc">{{ paperInfo.info }}</div>
    <div v-for="(topic, idx) in topics" :key="topic.id" class="qd-topic">
      <div class="qd-topic-title">
        <span v-if="topic.required" class="qd-required">*</span>
        {{ idx + 1 }}.{{ topic.title }}
        <span v-if="topic.type === 'single'">[单选]</span>
        <span v-else-if="topic.type === 'multi'">[多选]</span>
      </div>
      <!-- 单选 -->
      <van-radio-group v-if="topic.type === 'single'" v-model="answers[topic.id]" direction="vertical"
        :disabled="isUpdata">
        <van-radio v-for="opt in topic.options" :key="opt.id" :name="opt.id" :disabled="isUpdata">{{ opt.text
        }}</van-radio>
      </van-radio-group>
      <!-- 多选 -->
      <van-checkbox-group v-else-if="topic.type === 'multi'" v-model="answers[topic.id]" direction="vertical"
        :disabled="isUpdata">
        <van-checkbox v-for="opt in topic.options" :key="opt.id" :name="opt.id" :disabled="isUpdata">{{ opt.text
        }}</van-checkbox>
      </van-checkbox-group>
      <!-- 文本 -->
      <van-field v-else-if="topic.type === 'text'" v-model="answers[topic.id]" type="textarea" rows="3"
        :placeholder="'请输入'" class="qd-textarea" :disabled="isUpdata" />
    </div>
    <van-button type="primary" class="qd-submit" block size="small" :loading="submitting"
      :disabled="isEnd || submitting || isUpdata" @click="handleSubmit">
      {{ examStatus === 3 ? '重新提交' : '提交' }}
    </van-button>
  </div>
</template>
<script>
export default { name: 'QuestionnaireDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
const router = useRouter()
// import { formatDate } from '@/assets/js/utils.js'
const route = useRoute()
const title = ref('问卷调查详情')
const examineId = ref('')
const paperInfo = ref({})
const topics = ref([])
const answers = ref({})
const examStatus = ref(2) // 默认未提交
const isEnd = ref(false)
const submitting = ref(false)
const isUpdata = ref(false)
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  getData()
})
const getData = () => {
  console.log('route.query.paperStatus==>', route.query.paperStatus)
  console.log('route.query.examStatus==>', route.query.examStatus)
  if (route.query.paperStatus == '已结束') {
    isUpdata.value = true
    if (route.query.examStatus == '3') {
      studypapertopicMytopics()
    } else {
      studypaperExampaper()
    }
  } else {
    fetchDetail()
  }
}
// 获取详情
const fetchDetail = async () => {
  const { data } = await api.studyexamineAdd({ form: { paperId: route.query.id, terminalName: 'APP' } })
  examineId.value = data.id
  paperInfo.value = data.paper || {}
  examStatus.value = data.examStatus
  isUpdata.value = data.examStatus == '3' ? true : false
  if (examStatus.value == 3) {
    studypapertopicMytopics()
  } else {
    studypaperExampaper()
  }
}
const studypapertopicMytopics = async () => {
  const { data } = await api.studypapertopicMytopics({ examineId: route.query.examineId || examineId.value, needStudyInfo: true })
  paperInfo.value = data
  topics.value = (data.answerVos || []).map(item => ({
    id: item.id,
    type: item.studyTopicForm, // 'single' | 'multi' | 'text'
    title: item.title,
    required: item.isNeed === 1,
    options: (item.options || []).map(opt => ({
      id: opt.id,
      text: opt.optionTitle,
      optionCode: opt.optionCode
    }))
  }))
  answers.value = {};
  (data.answerVos || []).forEach(item => {
    if (item.studyTopicForm === 'multi') {
      const answerCodes = (item.answer || '').split(',').map(s => s.trim()).filter(Boolean);
      const selectedIds = (item.options || [])
        .filter(opt => answerCodes.includes(opt.optionCode))
        .map(opt => opt.id);
      answers.value[item.id] = selectedIds;
    } else if (item.studyTopicForm === 'single') {
      const selected = (item.options || []).find(opt => opt.optionCode === item.answer);
      answers.value[item.id] = selected ? selected.id : '';
    } else if (item.studyTopicForm === 'text') {
      answers.value[item.id] = item.answer || '';
    }
  })
}
const studypaperExampaper = async () => {
  const { data } = await api.studypaperExampaper({ detailId: route.query.id })
  // 1. 问卷信息
  paperInfo.value = {
    ...data,
    paperId: data.id // 保证后续提交参数有 paperId 字段
  }
  // 2. 题目列表
  topics.value = (data.topics || []).map(item => ({
    id: item.id,
    type: item.studyTopicForm, // 'single' | 'multi' | 'text'
    title: item.title,
    required: item.isNeed === 1,
    options: (item.options || []).map(opt => ({
      id: opt.id,
      text: opt.optionTitle,
      optionCode: opt.optionCode
    }))
  }))
  // 3. 初始化答案
  answers.value = {}
  topics.value.forEach(t => {
    if (t.type === 'multi') answers.value[t.id] = []
    else answers.value[t.id] = ''
  })
}
const handleSubmit = async () => {
  if (submitting.value) return
  if (!validateRequired()) {
    alert('请填写所有必填项')
    return
  }
  submitting.value = true
  try {
    const params = {
      form: {
        id: examineId.value,
        paperId: paperInfo.value.paperId || paperInfo.value.id,
        terminalName: 'APP'
      },
      answers: buildSubmitParams()
    }
    const res = await api.studyexamineSubmitquestion(params)
    if (res.code == 200) {
      showToast('提交成功')
      setTimeout(() => {
        router.back()
      }, 1000);
    }
  } catch (e) {
    showToast('提交失败')
  } finally {
    submitting.value = false
  }
}
// 校验必填项
const validateRequired = () => {
  for (const topic of topics.value) {
    if (topic.required) {
      const val = answers.value[topic.id]
      if (topic.type === 'multi' && (!val || val.length === 0)) return false
      if ((topic.type === 'single' || topic.type === 'text') && (!val || val === '')) return false
    }
  }
  return true
}
// 组装提交参数
const buildSubmitParams = () => {
  return topics.value.map(topic => {
    let answer = ''
    if (topic.type === 'multi') {
      const selectedIds = answers.value[topic.id] || []
      const codes = topic.options
        .filter(opt => selectedIds.includes(opt.id))
        .map(opt => opt.optionCode)
      answer = codes.join(',')
    } else if (topic.type === 'single') {
      const selectedId = answers.value[topic.id]
      const opt = topic.options.find(opt => opt.id === selectedId)
      answer = opt ? opt.optionCode : ''
    } else if (topic.type === 'text') {
      answer = answers.value[topic.id] || ''
    }
    return {
      paperTopicId: topic.id,
      answer
    }
  })
}
</script>
<style lang="scss" scoped>
.QuestionnaireDetails {
  max-width: 420px;
  margin: 0 auto;
  background: #fff;
  padding: 24px 12px 60px 12px;
  height: 100%;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  overflow: auto;

  .qd-title {
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 8px;
    color: #222;
    letter-spacing: 1px;
  }

  .qd-desc {
    color: #888;
    font-size: 15px;
    margin-bottom: 24px;
    text-align: center;
    line-height: 1.6;
  }

  .qd-topic {
    margin-bottom: 15px;
    padding: 18px 16px 14px 16px;
    background: #f8faff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);

    .qd-topic-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
      display: flex;
      align-items: center;

      .qd-required {
        color: #ff4d4f;
        margin-right: 4px;
        font-size: 18px;
      }
    }

    .van-radio-group,
    .van-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 6px;

      .van-radio,
      .van-checkbox {
        background: #fff;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 15px;
        transition: box-shadow 0.2s;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

        .van-radio__icon,
        .van-checkbox__icon {
          font-size: 20px;
        }
      }
    }

    .qd-textarea {
      margin-top: 10px;

      .van-field__control {
        min-height: 60px;
        font-size: 15px;
        border-radius: 8px;
        background: #f4f6fa;
        padding: 10px;
      }
    }
  }

  .qd-submit {
    width: 100%;
    margin-top: 24px;
    font-size: 18px;
    border-radius: 24px;
    height: 48px;
    background: linear-gradient(90deg, #2196f3 0%, #42a5f5 100%);
    color: #fff;
    font-weight: 600;
    letter-spacing: 2px;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.10);
    border: none;
    transition: background 0.2s;

    &:active {
      background: linear-gradient(90deg, #1976d2 0%, #2196f3 100%);
    }

    &:disabled {
      background: #cfd8dc;
      color: #fff;
    }
  }
}
</style>