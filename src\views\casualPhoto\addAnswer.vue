<template>
  <div class="addAnswer">
    <div>
      <van-form @submit="onSubmit">
        <van-cell-group>
          <div class="flex_box cellBox">
            <van-cell>
              <template #title>
                <span class="red">*</span>
                <span class="cellTitle">回复人</span>
              </template>
              <template #value>
                <span class="cellTitle">{{ user.userName }}</span>
              </template>
            </van-cell>
          </div>
          <div class="flex_box cellBox">
            <van-cell>
              <template #title>
                <span class="red">*</span>
                <span class="cellTitle">回复时间</span>
              </template>
              <template #value>
                <span>{{ formatDate(new Date(), 'YYYY-MM-DD') }}</span>
              </template>
            </van-cell>
          </div>
          <div class="cellBox">
            <van-cell :border="false">
              <template #title>
                <span class="red">*</span>
                <span class="cellTitle">回复内容</span>
              </template>
            </van-cell>
            <van-field v-model="form.content"
                       :border="false"
                       rows="4"
                       autosize
                       type="textarea"
                       maxlength="1000"
                       show-word-limit
                       :rules="[{ required: true, message: '请输入回复内容' }]"
                       placeholder="请输入回复内容" />
            <div class="cellBtnBox">
              <div class="cellBtn"><van-image class="cellBtnImg"
                           :src="require('@/assets/img/Text_1.png')"></van-image>语音转文字
              </div>
            </div>
          </div>
          <div class="cellBox">
            <van-cell :border="false">
              <template #title>
                <span class="red">*</span>
                <span class="cellTitle">回复附件</span>
              </template>
            </van-cell>
            <div class="cellImgBtnBox ">
              <van-button icon="plus"
                          plain
                          hairline
                          type="primary"
                          size="large"
                          @click="onClickFile(1)">附件</van-button>
            </div>
            <!-- <div class="flex_box cellImgBtnBox flex_justify_content">
              <div class="cellBtnImgItem"
                   @click="onClickFile(1)">
                <van-image class="cellBtnImg"
                           :src="require('@/assets/img/Pic_1.png')"></van-image>
                <div class="cellImgBtnText">
                  图片
                </div>
              </div>
              <div class="cellBtnImgItem"
                   @click="onClickFile(2)">
                <van-image class="cellBtnImg"
                           :src="require('@/assets/img/Video_1.png')"></van-image>
                <div class="cellImgBtnText">
                  视频
                </div>
              </div>
              <div class="cellBtnImgItem"
                   @click="onClickFile(3)">
                <van-image class="cellBtnImg"
                           :src="require('@/assets/img/Voice_1.png')"></van-image>
                <div class="cellImgBtnText">
                  语音输入
                </div>
              </div>
            </div>
            <div class="cellImgBtnTip">最多上传4张图片，单个不超过10M；视频时长不超过30s。</div> -->
            <ul v-if="nImgs.length != 0"
                class="select_img_box flex_box T-flex-flow-row-wrap">
              <li v-for="(item, index) in nImgs"
                  :key="index">
                <img v-if="item.state > 0"
                     :src="item.state == 1 ? require('../../assets/img/icon_doubt.png') : item.state == 2 ? require('../../assets/img/icon_upload_success.png') : require('../../assets/img/icon_upload_fail.png')"
                     class="select_img_state" />
                <img src="../../assets/img/icon_upload_delete.png"
                     class="select_img_del"
                     @click="deleteSelectImg(item, index)" />
                <div :style="'background-image:url(' + item.url + ')'"
                     @click="openSelectImg(nImgs, index)"></div>
              </li>
            </ul>
          </div>
        </van-cell-group>
        <div style="margin: 16px;">
          <van-button block
                      type="primary"
                      native-type="submit">
            提交
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
  <van-uploader style="display: none;"
                v-model="fileList"
                :max-count='4'
                :after-read="afterRead"
                ref="chatImg">
  </van-uploader>
</template>
<script>
export default { name: 'addAnswer' }
</script>
<script setup>
import api from '@/api'
import { useRoute, useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import { showToast, showConfirmDialog, showImagePreview } from 'vant'
import { formatDate } from '@/assets/js/utils.js'
const route = useRoute()
const router = useRouter()
console.log(router);
const title = ref(route.query.title || '答复')
// const roleId = ref(sessionStorage.getItem('roleId') || '1')
const form = ref({
  title: '',
  content: ''
})

const nImgs = ref([])
const chatImg = ref(null)
const fileList = ref([])
const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  setTimeout(() => {
    onRefresh()
  }, 100)
})

const onSubmit = (values) => {
  if (values) {
    showConfirmDialog({
      title: '提示',
      confirmButtonColor: '#4488EB',
      message: '是否提交？'
    }).then(async () => {
      var param = {
        detailId: route.query.id,
        recordContent: form.value.content,
        recordTime: new Date().getTime(),
        attachmentIds: nImgs.value.map(v => v.uploadId).join(','),
        // userId: user.value.id,
        // userName: user.value.userName,
        // userImg: user.value.headImg,
        // mobile: user.value.mobile,
      }
      await api.zyConvenientlyPatAnswer(param)
      showToast('提交成功')
      setTimeout(() => {
        onClickLeft()
      }, 1000)
    }).catch(() => {
      // onLoad()
    })
  } else {
    showConfirmDialog.alert({
      message: '请填写完整信息'
    })
  }
}
const onClickFile = (type) => {
  console.log(type)
  if (type === 1) {
    if (nImgs.value.length >= 4) {
      showToast('最多上传4张图片')
      return
    }
    // 图片
    chatImg.value.chooseFile()
    // $api.uploadImg()
  } else if (type === 2) {
    // 视频
    // eslint-disable-next-line no-undef
    wx.invoke('chooseVideo', {
      sourceType: ['camera'] // 可以指定来源是相册还是相机，默认二者都有
    }, function (res) {
      console.log(res)
      // var localIds = typeof res.localIds === 'object' ? res.localIds : JSON.parse(res.localIds) // 返回生成视频的本地ID，需要typeof判断兼容各个终端
      // var thumbnail = res.thumbnail // 视频封面缩略图
    })
  } else if (type === 3) {
    // 语音
    // $api.uploadVoice()
  }
}
const onRefresh = () => {
}

const afterRead = async (file) => {
  const item = { url: file.content, uploadUrl: '', uploadId: '', state: 0, module: '' }
  const formData = new FormData()
  formData.append('file', file.file)
  // formData.append('module', 'generalComments')
  // formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
  const ret = await api.globalUpload(formData, (res) => { })
  if (ret) {
    var info = ret.data
    item.state = 2
    item.uploadUrl = api.fileURL(info.id) + info.newFileName || ''
    item.uploadId = info.id || ''
  } else {
    item.state = 3
    item.error = ret.errmsg || ''
  }
  nImgs.value.push(item)
  console.log(nImgs.value);

}
// 删除选择图片
const deleteSelectImg = (_item, _index) => {
  nImgs.value = nImgs.value.filter((item) => item.uploadId !== _item.uploadId)
}
const openSelectImg = (imgs, _index) => {
  var img = []
  imgs.forEach(element => {
    img.push(element.url)
  })
  showImagePreview(img, _index)
}
const onClickLeft = () => history.back()
</script>
<style lang="scss">
.addAnswer {
  background: #F4F5F9;

  .van-cell-group {
    background-color: rgba(0, 0, 0, 0);
  }

  .cellImgBtnBox {
    background: #fff;
    padding: 10px;

    .cellBtnImgItem {
      text-align: center;
      background: #F6F6F6;
      border-radius: 8px;
      width: 72px;
      height: 72px;
      margin: 10px 15px;

      .cellBtnImg {
        width: 24px;
        height: 24px;
        margin: 12px 0 4px 0;
      }

      .cellImgBtnText {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .cellImgBtnTip {
    background: #fff;
    padding: 5px 18px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .select_img_box {
    max-height: 130px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 10px;
    box-sizing: border-box;
    background: #fff;
    padding: 10px;
  }

  .select_img_box li {
    width: 25%;
    position: relative;
    padding: 0 6px;
    box-sizing: border-box;
  }

  .select_img_box li div {
    width: 100%;
    height: 75px;
    background-size: cover;
    -webkit-background-size: cover;
    background-position: 50%;
  }

  .select_img_box li .select_img_del {
    position: absolute;
    top: 3px;
    right: 10px;
  }

  .select_img_box li .select_img_state {
    position: absolute;
    bottom: 3px;
    right: 10px;
    width: 16px;
    height: 16px;
  }

  .cellBox {
    margin-bottom: 8px;

    .cellTitleBox {
      width: 100px;
      min-height: 46px;
    }

    .red {
      min-height: 46px;
      font-weight: 400;
      font-size: 16px;
      color: #FF0705;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-right: 4px;
    }

    .cellTitle {
      min-height: 46px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .cellIcon {
      margin-top: 3px;
    }

    .cellBtnBox {
      position: relative;
      height: 43px;
      background: #fff;

      .cellBtn {
        position: absolute;
        right: 11px;
        width: 165px;
        height: 33px;
        border-radius: 17px 17px 17px 17px;
        border: 1px solid #4488EB;
        text-align: center;
        line-height: 33px;
        font-weight: 400;
        font-size: 14px;
        color: #4488EB;

        .cellBtnImg {
          width: 12px;
          height: 12px;
          margin-right: 4px;
          vertical-align: middle;
          margin-top: -2px;
        }
      }
    }
  }
}
</style>
