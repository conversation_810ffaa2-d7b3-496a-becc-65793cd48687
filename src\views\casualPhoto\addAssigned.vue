<template>
  <div class="addAssigned">
    <div>
      <van-form @submit="onSubmit">
        <van-cell-group>
          <div class="flex_box cellBox">
            <van-cell class="cellTitleBox">
              <template #title>
                <span class="red">*</span>
                <span class="cellTitle">转交</span>
              </template>
            </van-cell>
            <div class="cellRightBox">
              <van-radio-group v-model="assignedType"
                               direction="horizontal">
                <van-radio name="1"
                           v-if="roleId !== '4'">转上级</van-radio>
                <van-radio name="2"
                           v-if="roleId === '3' || roleId === '4'">转12345</van-radio>
              </van-radio-group>
            </div>
          </div>
          <div class="flex_box cellBox"
               v-if="assignedType === '2'">
            <van-cell is-link
                      @click="onClickType">
              <template #title>
                <span class="red">*</span>
                <span class="cellTitle">建议办理单位</span>
              </template>
              <template #value>
                <span>{{ typeData.valueName ? typeData.valueName : '点击选择建议办理单位' }}</span>
              </template>
            </van-cell>
          </div>
          <div class="cellBox">
            <van-cell :border="false">
              <template #title>
                <span class="red">*</span>
                <span class="cellTitle">理由</span>
              </template>
            </van-cell>
            <van-field v-model="form.content"
                       :border="false"
                       rows="4"
                       autosize
                       type="textarea"
                       maxlength="1000"
                       show-word-limit
                       :rules="[{ required: true, message: '请输入理由' }]"
                       placeholder="请输入理由" />
            <div class="cellBtnBox">
              <div class="cellBtn"><van-image class="cellBtnImg"
                           :src="require('@/assets/img/Text_1.png')"></van-image>语音转文字
              </div>
            </div>
          </div>
        </van-cell-group>
        <div style="margin: 16px;">
          <van-button block
                      type="primary"
                      native-type="submit">
            提交
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
  <van-action-sheet v-model:show="showType"
                    :actions="typeData.data"
                    @select="onSelect" />
  <van-uploader style="display: none;"
                v-model="fileList"
                :max-count='4'
                :after-read="afterRead"
                ref="chatImg">
  </van-uploader>
</template>
<script>
export default { name: 'addAssigned' }
</script>
<script setup>
import api from '@/api'
import { useRoute, useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
const route = useRoute()
const router = useRouter()
console.log(router);

const roleId = ref(sessionStorage.getItem('roleId') || '1')
const title = ref(route.query.title || '转交')
const form = ref({
  title: '',
  content: ''
})
const showType = ref(false)
const typeData = ref({
  value: '',
  valueName: '',
  data: []
})

const nImgs = ref([])
const chatImg = ref(null)
const assignedType = ref('1')
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  if (roleId.value === '2') {
    typeData.value.data = [
      { name: '长沙市', value: '1' },
      { name: '湘潭市', value: '2' },
      { name: '株洲市', value: '3' },
      { name: '衡阳市', value: '4' },
      { name: '岳阳市', value: '5' },
      { name: '常德市', value: '6' },
      { name: '益阳市', value: '7' },
      { name: '娄底市', value: '8' },
      { name: '邵阳市', value: '9' },
      { name: '郴州市', value: '10' },
      { name: '永州市', value: '11' },
      { name: '怀化市', value: '12' },
      { name: '湘西州', value: '13' }
    ]
  } else if (roleId.value === '3') {
    // 市公安局 市财政局 市水利局 市民政局 市司法局 市国资委 市卫生健康委 市教育局
    typeData.value.data = [
      { name: '市公安局', value: '1' },
      { name: '市财政局', value: '2' },
      { name: '市水利局', value: '3' },
      { name: '市民政局', value: '4' },
      { name: '市司法局', value: '5' },
      { name: '市国资委', value: '6' },
      { name: '市卫生健康委', value: '7' },
      { name: '市教育局', value: '8' }
    ]
  } else if (roleId.value === '4') {
    // 省发展改革委 省教育厅 省司法厅 省公安厅 省民政厅 省农业农村厅 省国资委 省统计局
    assignedType.value = '2'
    typeData.value.data = [
      { name: '省发展改革委', value: '1' },
      { name: '省教育厅', value: '2' },
      { name: '省司法厅', value: '3' },
      { name: '省公安厅', value: '4' },
      { name: '省民政厅', value: '5' },
      { name: '省农业农村厅', value: '6' },
      { name: '省国资委', value: '7' },
      { name: '省统计局', value: '8' }
    ]
  }
  setTimeout(() => {
    onRefresh()
  }, 100)
})

const onClickType = () => {
  if (!route.query.typeId) {
    showType.value = true
  }
}
const onSelect = (e) => {
  console.log(e)
  typeData.value.value = e.value
  typeData.value.valueName = e.name
  typeData.value.data.map((item) => {
    if (item.value === e.value) {
      item.color = '#4488EB'
    } else {
      item.color = ''
    }
  })
  showType.value = false
}
const onSubmit = (values) => {
  // if (!typeData.value.value) {
  //   showToast('请选择问题类型')
  //   return
  // }
  if (values) {
    showConfirmDialog({
      title: '提示',
      confirmButtonColor: '#4488EB',
      message: '是否提交？'
    }).then(() => {
      // onLoad()
      if (assignedType.value === '1') {
        api.assignZyConvenientlyPat({ detailId: route.query.id, assignReason: form.value.content })
      }
      if (assignedType.value === '2') {
        api.assign12345ZyConvenientlyPat({ detailId: route.query.id, assignReason: form.value.content })
      }
      showToast('提交成功')
      setTimeout(() => {
        onClickLeft()
      }, 1000)
    }).catch(() => {
      // onLoad()
    })
  } else {
    showConfirmDialog.alert({
      message: '请填写完整信息'
    })
  }
}

const onRefresh = () => {
}

const afterRead = async (file) => {
  const item = { url: file.content, uploadUrl: '', uploadId: '', state: 0, module: '' }
  const formData = new FormData()
  formData.append('file', file.file)
  // formData.append('module', 'generalComments')
  // formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
  const ret = await api.globalUpload(formData, (res) => { })
  if (ret) {
    var info = ret.data
    item.state = 2
    item.uploadUrl = api.fileURL(info.id) + info.newFileName || ''
    item.uploadId = info.id || ''
  } else {
    item.state = 3
    item.error = ret.errmsg || ''
  }
  nImgs.value.push(item)
  console.log(nImgs.value);

}

const onClickLeft = () => history.back()
</script>
<style lang="scss">
.addAssigned {
  background: #F4F5F9;

  .van-cell-group {
    background-color: rgba(0, 0, 0, 0);
  }

  .cellImgBtnBox {
    background: #fff;

    .cellBtnImgItem {
      text-align: center;
      background: #F6F6F6;
      border-radius: 8px;
      width: 72px;
      height: 72px;
      margin: 10px 15px;

      .cellBtnImg {
        width: 24px;
        height: 24px;
        margin: 12px 0 4px 0;
      }

      .cellImgBtnText {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .cellImgBtnTip {
    background: #fff;
    padding: 5px 18px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .select_img_box {
    max-height: 130px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 10px;
    box-sizing: border-box;
    background: #fff;
    padding: 10px;
  }

  .select_img_box li {
    width: 25%;
    position: relative;
    padding: 0 6px;
    box-sizing: border-box;
  }

  .select_img_box li div {
    width: 100%;
    height: 75px;
    background-size: cover;
    -webkit-background-size: cover;
    background-position: 50%;
  }

  .select_img_box li .select_img_del {
    position: absolute;
    top: 3px;
    right: 10px;
  }

  .select_img_box li .select_img_state {
    position: absolute;
    bottom: 3px;
    right: 10px;
    width: 16px;
    height: 16px;
  }

  .cellRightBox {
    flex: 1;
    background: #fff;
    padding-top: 11px;
  }

  .cellBox {
    margin-bottom: 8px;

    .cellTitleBox {
      width: 100px;
      min-height: 46px;
    }

    .red {
      min-height: 46px;
      font-weight: 400;
      font-size: 16px;
      color: #FF0705;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-right: 4px;
    }

    .cellTitle {
      min-height: 46px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .cellIcon {
      margin-top: 3px;
    }

    .cellBtnBox {
      position: relative;
      height: 43px;
      background: #fff;

      .cellBtn {
        position: absolute;
        right: 11px;
        width: 165px;
        height: 33px;
        border-radius: 17px 17px 17px 17px;
        border: 1px solid #4488EB;
        text-align: center;
        line-height: 33px;
        font-weight: 400;
        font-size: 14px;
        color: #4488EB;

        .cellBtnImg {
          width: 12px;
          height: 12px;
          margin-right: 4px;
          vertical-align: middle;
          margin-top: -2px;
        }
      }
    }
  }
}
</style>
