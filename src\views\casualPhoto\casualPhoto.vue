<template>
  <div class="casualPhoto">
    <div class="casualPhotoContent" v-if="roleId === '1'">
      <div class="muneBox flex_box ">
        <div class="muneBoxItem" v-for="(item, index) in dataList" :key="index" @click="itemClick(item)">
          <img :src="item.icon" alt="">
          <div class="muneBoxItemText">{{ item.name }}</div>
        </div>
      </div>
      <div class="bottomBox flex_box flex_justify_content" @click="itemClick(addBtn)">
        <img :src="addBtn.icon" alt="">
        <div class="muneBoxItemText">{{ addBtn.name }}</div>
      </div>
    </div>
    <casualPhotoList v-else></casualPhotoList>
  </div>
</template>
<script>
export default { name: 'casualPhoto' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import casualPhotoList from './casualPhotoList.vue'
const route = useRoute()
const router = useRouter()

const roleId = ref('1')
const addBtn = ref({
  id: 'addBtn',
  name: '我提交的随手拍',
  // icon: require('../../assets/img/icon_7.png')
})
const dataList = ref([{
  id: '1',
  name: '法治护绿',
  // icon: require('../../assets/img/icon_1.png')
},
{
  id: '2',
  name: '法治护安',
  // icon: require('../../assets/img/icon_2.png')
},
{
  id: '3',
  name: '法治护企',
  // icon: require('../../assets/img/icon_3.png')
},
{
  id: '4',
  name: '基层治理',
  // icon: require('../../assets/img/icon_4.png')
},
{
  id: '5',
  name: '基层民生',
  // icon: require('../../assets/img/icon_5.png')
},
{
  id: '6',
  name: '其他',
  // icon: require('../../assets/img/icon_6.png')
}
])
onMounted(() => {
  document.title = '代表随手拍'
  route.query.id && (roleId.value = route.query.id || '1')
  getStationUserInfo()
  getDictionaryData()
})

const getStationUserInfo = async () => {
  const user = JSON.parse(sessionStorage.getItem('user'))
  const { data } = await api.getStationUserInfo({ "mobile": user.mobile })
  sessionStorage.setItem('stationUserInfo', JSON.stringify(data))
  if (data.ifMember) {//代表
    roleId.value = '1'
  } else if (data.ifStationWorker) {//基层管理工作人在
    roleId.value = '2'
  } else if (data.ifStationAdmin) {//省市代工委管理员
    roleId.value = '3'
  }
  sessionStorage.setItem('roleId', roleId.value)
}
const getDictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['question_type'] })
  var datas = data.question_type || []
  datas.forEach((item, index) => {
    // item.icon = require('../../assets/img/icon_' + (index + 1) + '.png')
  });
  dataList.value = [...datas]
}
const itemClick = (item) => {
  if (item.id === 'addBtn') {
    // 跳转到随手拍列表页面
    router.push({ path: 'casualPhotoList', query: { typeId: item.id, title: item.name, roleId: roleId.value } })
  } else {
    // 跳转到提交随手拍页面
    router.push({ path: 'addCasualPhoto', query: { typeId: item.id, title: item.name, roleId: roleId.value } })
  }
}
</script>
<style lang="scss">
.casualPhoto {
  width: 100%;
  height: 100%;
  background: #F4F5F9;

  .muneBox {
    flex-wrap: wrap;
    width: calc(100% - 20px);
    margin-left: 10px;
    padding-top: 27px;

    .muneBoxItem {
      width: calc(50% - 10px);
      margin: 0 5px 14px 5px;
      border-radius: 8px 8px 8px 8px;
      text-align: center;
      background: #fff;

      img {
        width: 51px;
        height: 51px;
        margin-top: 18px;
      }

      .muneBoxItemText {
        font-weight: 400;
        font-size: 17px;
        color: #333333;
        text-align: center;
        margin: 13px 0 18px 0;
      }
    }
  }

  .bottomBox {
    width: calc(100% - 30px);
    margin-left: 15px;
    padding-top: 18px;
    padding-bottom: 17px;
    text-align: center;
    background: #fff;
    border-radius: 8px 8px 8px 8px;

    img {
      width: 51px;
      height: 51px;
    }

    .muneBoxItemText {
      font-weight: 400;
      font-size: 17px;
      color: #333333;
      margin-top: 10px;
      margin-left: 28px;
    }
  }
}
</style>
