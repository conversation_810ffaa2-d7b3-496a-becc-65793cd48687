<template>
  <div class="casualPhotoList">
    <div class="casualPhotoListContent">
      <div class="addBtnBox" v-if="roleId == '1'" @click="addClick()">
        <van-image class="addBtnImg" :src="addBtn.icon" alt=""></van-image>
      </div>
      <van-search v-model="keyword" show-action shape="round" placeholder="请输入搜索关键词" @search="onSearch">
        <template #action>
          <div class="searchBtn" @click="show = true">筛选<van-icon size="18" :color="'#000'" name="filter-o" /></div>
        </template>
      </van-search>
      <van-tabs v-if="switchs.data.length" v-model:active="switchs.value" :color="'#4488EB '" @click-tab="onClickTab">
        <van-tab :title="item.label" :name="item.value" v-for="item in switchs.data" :key="item.value"></van-tab>
      </van-tabs>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" :finished="finished" :finished-text="dataList.length == 0 ? '' : '没有更多了'"
          offset="52" @load="onLoad">
          <ListItem v-for="item in dataList" :key="item" :itemData="item" @click="openDetail(item)"></ListItem>
          <van-empty v-if="dataList.length == 0" description="暂无数据" />
        </van-list>
        <div class="btnBox" @click="smartPage()">智能分析</div>
      </van-pull-refresh>
      <!-- <van-popup v-model:show="timeShow"
                 round
                 position="bottom"
                 :style="{ height: '40%' }">
        <van-date-picker v-model="currentDate"
                         type="date"
                         :min-date="minDate"
                         :max-date="maxDate"
                         :formatter="formatter"
                         @confirm="onConfirm"
                         @cancel="onCancel"
                         :title="timeTitle" />
      </van-popup> -->
      <van-calendar v-model:show="timeShow" :default-date="currentDate" :title="timeTitle" :min-date="minDate"
        :max-date="maxDate" @confirm="onConfirm" />
      <van-popup v-model:show="show" round position="bottom" :style="{ height: '80%' }">
        <div class="popupBox">
          <div class="popupBoxContent">
            <div class="sreenTitle">{{ statusData.title }}</div>
            <div class="sreenBox flex_box ">
              <div v-for="(item, index) in statusData.data" :key="'statusData' + index"
                :class="'sreenItem ' + (item.value == statusData.value ? 'active' : '')" @click="tagClick(item, index)">
                {{ item.label }}
              </div>
            </div>
            <div class="sreenTitle">时间段</div>
            <div class="sreenBox flex_box" style="flex-wrap: nowrap;">
              <div class="timeItem flex_box" @click="timeClick(1)">
                <!-- <van-image class="timeItemImg"
                           :src="require('@/assets/img/Calendar_1.png')"
                           alt=""></van-image> -->
                <div class="timeItemText">{{ startTime ? startTime : '开始时间' }}</div>
              </div>
              <div class="timeItemline">--</div>
              <div class="timeItem flex_box" @click="timeClick(2)">
                <!-- <van-image class="timeItemImg"
                           :src="require('@/assets/img/Calendar_1.png')"
                           alt=""></van-image> -->
                <div class="timeItemText">{{ endTime ? endTime : '结束时间' }}</div>
              </div>
            </div>
            <div class="sreenTitle">{{ typeData.title }}</div>
            <div class="sreenBox flex_box ">
              <div v-for="(item, index) in typeData.data" :key="'typeData' + index"
                :class="'sreenItem ' + (item.value == typeData.value ? 'active' : '')" @click="typeClick(item, index)">
                {{ item.label }}
              </div>
            </div>
          </div>
          <div class="popupBoxBtn flex_box flex_justify_content">
            <van-button type="primary" size="large" plain hairline @click="clear()">清除</van-button>
            &nbsp;&nbsp;
            <van-button type="primary" size="large" @click="show = false; onRefresh();">确定</van-button>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>
<script>
export default { name: 'casualPhotoList' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { formatDate } from '@/assets/js/utils.js'
import ListItem from './components/listItem.vue'
const route = useRoute()
const router = useRouter()
const title = ref('随手拍列表')
const stationUserInfo = ref(JSON.parse(sessionStorage.getItem('stationUserInfo')) || '')
const timeShow = ref(false) //时间选择器
const currentDate = ref(new Date())
const timeTitle = ref('')
const startTime = ref('')
const endTime = ref('')
const minDate = ref(new Date(2020, 0, 1))
const maxDate = ref()
const typeId = ref('')
const roleId = ref('')
const addBtn = ref({
  name: '新增',
  // icon: require('../../assets/img/<EMAIL>')
})
const show = ref(false)//筛选弹窗
const keyword = ref('')//搜索关键词 '',
// const showSkeleton = ref(true)//骨架屏
const loading = ref(false)//加载中
const finished = ref(false)//是否加载完成
const refreshing = ref(false)//下拉刷新
const pageNo = ref(1)//当前页码
const pageSize = ref(10)//每页条数
const total = ref(0)//总条数
const dataList = ref([
  // {
  //   title: '道路破损严重',
  //   content: '违规施工没有任何安全措施，会出重大影响',
  //   createDate: '2022-12-12 12:12:12',
  //   answerEndDate: '2025-05-12 12:12:12',
  //   status: '已转交',
  //   userName: '张三',
  //   tag: '长沙市代表团人大代表',
  //   userImg: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747',
  //   stationName: '东方红街道代表联系站',
  //   commentCount: 1,
  //   likeCount: 1,
  //   isLike: true,
  //   type: '问题类型一',
  //   imgList: [{
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }, {
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }]
  // }, {
  //   title: '违规施工没有任何安全措施，会出重大影响',
  //   content: '违规施工没有任何安全措施，会出重大影响',
  //   createDate: '2022-12-12 12:12:12',
  //   answerEndDate: '2025-05-08 12:12:12',
  //   status: '已回复',
  //   userName: '张三',
  //   tag: '长沙市代表团人大代表',
  //   userImg: 'https://qcloud.dpfile.com/pc/eT0GRVLwTSRN-TYm8dYLzoNNJI2R54mx073RDPRMJZkFHFxGOJtZhoAD7l3hnmGC.jpg',
  //   stationName: '东方红街道代表联系站',
  //   commentCount: 1,
  //   likeCount: 1,
  //   type: '问题类型一',
  //   imgList: [{
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }, {
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }],
  //   replydata: {
  //     stationName: '东方红街道代表联系站',
  //     reply: '代表您好，您反映的问题已办结，感谢您的支持！',
  //     imgList: [{
  //       url: 'https://pics5.baidu.com/feed/a044ad345982b2b78ab10bad204736e377099ba7.jpeg@f_auto?token=72e1cf5205d91e7eb227052abdda3405'
  //     }, {
  //       url: 'https://www.jdnews.com.cn/xwjsb/images/2023-07/14/260a1cfc-a81d-4a48-a478-14b80c38f397.jpg'
  //     }, {
  //       url: 'https://img0.baidu.com/it/u=3548398977,4213109153&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067'
  //     }, {
  //       url: 'http://img2.baidu.com/it/u=2971848481,3999724549&fm=253&app=138&f=JPEG?w=427&h=759'
  //     }, {
  //       url: 'http://img0.baidu.com/it/u=3178311242,3030645591&fm=253&app=138&f=JPEG?w=427&h=759'
  //     }]
  //   }
  // }, {
  //   title: '违规施工没有任何安全措施，会出重大影响',
  //   content: '违规施工没有任何安全措施，会出重大影响',
  //   createDate: '2022-12-12 12:12:12',
  //   answerEndDate: '2023-12-12 12:12:12',
  //   status: '待办理',
  //   userName: '张三',
  //   tag: '长沙市代表团人大代表',
  //   userImg: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747',
  //   stationName: '东方红街道代表联系站',
  //   commentCount: 1,
  //   likeCount: 1,
  //   type: '问题类型一',
  //   imgList: [{
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }, {
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }]
  // }, {
  //   title: '违规施工没有任何安全措施，会出重大影响',
  //   content: '违规施工没有任何安全措施，会出重大影响',
  //   createDate: '2022-12-12 12:12:12',
  //   answerEndDate: '2023-12-12 12:12:12',
  //   status: '已回复',
  //   userName: '张三',
  //   tag: '长沙市代表团人大代表',
  //   userImg: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747',
  //   stationName: '东方红街道代表联系站',
  //   commentCount: 1,
  //   likeCount: 1,
  //   type: '问题类型一',
  //   imgList: [{
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }, {
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }]
  // }, {
  //   title: '违规施工没有任何安全措施，会出重大影响',
  //   content: '违规施工没有任何安全措施，会出重大影响',
  //   createDate: '2022-12-12 12:12:12',
  //   answerEndDate: '2023-12-12 12:12:12',
  //   status: '已回复',
  //   userName: '张三',
  //   tag: '长沙市代表团人大代表',
  //   userImg: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747',
  //   stationName: '东方红街道代表联系站',
  //   commentCount: 1,
  //   likeCount: 1,
  //   type: '问题类型一',
  //   imgList: [{
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }, {
  //     url: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747'
  //   }]
  // }
])
const switchs = ref({ value: '', data: [] })
const statusData = ref({ value: '', title: '是否回复', data: [{ label: '全部', value: '' }, { label: '已回复', value: '1' }, { label: '未回复', value: '0' }] })
// const timeData = ref('')
const typeData = ref({ value: '', title: '问题类型', data: [{ label: '全部', value: '' }, { label: '法治护绿', value: '1' }, { label: '法治护安', value: '2' }, { label: '法治护企', value: '3' }, { label: '基层治理', value: '4' }, { label: '基层民生', value: '5' }, { label: '其他', value: '6' }] })
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  route.query.roleId && (roleId.value = route.query.roleId || '1')
  sessionStorage.getItem('roleId') && (roleId.value = sessionStorage.getItem('roleId') || '1')
  switchs.value.value = '1'
  if (roleId.value === '1') {
    switchs.value.data = [{ label: '站点发布', value: '1' }, { label: '我提交的', value: '2' }]
  } else {
    switchs.value.data = [{ label: '所有', value: '1' }, { label: '待办理', value: '2' }, { label: '已回复', value: '3' }, { label: '已转交', value: '4' }]
  }
  getDictionaryData()
  setTimeout(() => {
    onRefresh()
  }, 100)
})
const getDictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['question_type'] })
  var datas = data.question_type || []
  datas.forEach(item => {
    item.value = item.id
  });
  typeData.value.data = [{ label: '全部', value: '' }, ...datas]
}
const addClick = () => {
  // 跳转到提交随手拍页面
  router.push({ path: 'addCasualPhoto', query: { title: '新增', typeId: typeId.value === 'addBtn' ? '' : typeId.value, roleId: roleId.value } })
}
const timeClick = (type) => {
  timeShow.value = true
  currentDate.value = new Date(endTime.value)
  if (type === 1) {
    timeTitle.value = '开始时间'
    minDate.value = new Date(2020, 0, 1)
    if (endTime.value) {
      maxDate.value = new Date(endTime.value)
    } else {
      maxDate.value = new Date()
    }
  } else {
    timeTitle.value = '结束时间'
    maxDate.value = new Date()
    if (startTime.value) {
      minDate.value = new Date(startTime.value)
    } else {
      minDate.value = new Date(2020, 0, 1)
    }
  }
}
// const formatter = (type, val) => {
//   if (type === 'year') {
//     return `${val}年`
//   } else if (type === 'month') {
//     return `${val}月`
//   } else if (type === 'day') {
//     return `${val}日`
//   }
//   return val
// }
const onConfirm = (value) => {
  if (timeTitle.value === '开始时间') {
    startTime.value = formatDate(value, 'YYYY-MM-DD')
  } else {
    endTime.value = formatDate(value, 'YYYY-MM-DD')
  }
  timeShow.value = false
}
// const onCancel = () => {
//   timeShow.value = false
// }
const clear = () => {
  startTime.value = ''
  endTime.value = ''
  statusData.value.value = ''
  typeData.value.value = ''
}
const tagClick = (item) => {
  statusData.value.value = item.value
}
const typeClick = (item) => {
  typeData.value.value = item.value
}
const openDetail = (item) => {
  router.push({ path: 'casualPhotoDetails', query: { id: item.id, title: item.title, typeId: typeId.value, roleId: roleId.value } })
}
const smartPage = () => {
  router.push({ path: 'smartPage', query: { title: '智能分析' } })
}
const onSearch = () => {
  onRefresh()
}
const onClickTab = () => {
  onRefresh()
}
const getList = async () => {
  const params = {
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    keyword: keyword.value
  }
  if (roleId.value === '1') {
    params.ifMember = true
    params.stationIds = stationUserInfo.value.stationIds
  }
  if (roleId.value === '2') {
    params.ifStationWorker = true
    params.stationIds = stationUserInfo.value.workerStationIds
  }
  if (switchs.value.value) {
    params.queryType = switchs.value.value
  }
  if (typeData.value.value) {
    params.questionType = typeData.value.value
  }
  if (statusData.value.value) {
    params.ifReply = statusData.value.value
  }
  if (startTime.value && endTime.value) {
    params.submitSatrtDate = startTime.value
    params.submitEndDate = endTime.value
  }
  const { data, total: totals, code } = await api.zyConvenientlyPatList(params)
  console.log(data)
  if (code === 200) {
    dataList.value = data || []
    loading.value = false
    finished.value = total.value <= pageNo.value * pageSize.value
    refreshing.value = false
    total.value = totals
  } else {
    loading.value = false
    finished.value = true
    refreshing.value = false
  }

}
const onRefresh = () => {
  pageNo.value = 1
  getList()
}
const onLoad = () => {
  if (dataList.value.length < total.value) {
    pageNo.value++
    getList()
  } else {
    finished.value = true
  }
}
</script>
<style lang="scss">
.casualPhotoList {
  width: 100%;
  height: 100%;
  background: #F4F5F9;
  overflow: auto;

  .btnBox {
    position: fixed;
    bottom: 40px;
    right: 20px;
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: var(--van-primary-color);
    border-radius: 20px 20px 20px 20px;
    color: #FFF;
  }

  .addBtnBox {
    background: #FFF;
    padding: 11px 12px;

    .addBtnImg {
      height: 108px;
    }
  }

  .searchBtn {
    width: 64px;
    height: 35px;
    background: #F1F1F1;
    border-radius: 18px 18px 18px 18px;
    text-align: center;
    line-height: 35px;
    font-size: 12px;
    color: #666666;
  }

  .popupBox {
    padding: 20px 20px;

    .sreenTitle {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .sreenBox {
      flex-wrap: wrap;
      margin: 10px 0 20px 0;

      .timeItemline {
        color: #C7C7C7;
        margin: 10px 5px 0 5px
      }

      .timeItemImg {
        width: 24px;
        height: 24px;
        margin: 0px 8px 0 8px;
      }

      .timeItemText {
        font-weight: 400;
        font-size: 14px;
        color: #AAAAAA;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .timeItem {
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
        align-items: center;
        border: 1px solid #F1F1F1;
        height: 40px;
        line-height: 40px;
        width: calc(50% - 10px);
      }

      .sreenItem {
        width: calc(50% - 10px);
        height: 40px;
        margin: 10px 5px;
        line-height: 40px;
        text-align: center;
        border-radius: 4px 4px 4px 4px;
        background: #F1F1F1;
        border: 1px solid #F1F1F1;
      }

      .active {
        background: #FFFFFF;
        color: #4488EB;
        border: 1px solid #4488EB;
      }
    }
  }
}
</style>
