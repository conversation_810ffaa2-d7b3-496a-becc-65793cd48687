<template>
  <div class="commentList">
    <div class="title">
      <div class="titleText">
        评论
      </div>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                :finished-text="dataList.length == 0 ? '' : '没有更多了'"
                offset="52"
                @load="onLoad">
        <commentItem v-for="item in dataList"
                     :key="item"
                     :itemData="item"
                     :id="props.id"
                     @callback="callback"
                     @reload="reload"></commentItem>
        <van-empty v-if="dataList.length == 0"
                   description="暂无数据" />
      </van-list>
    </van-pull-refresh>
    <!-- <van-popup v-model:show="timeShow"
                 round
                 position="bottom"
                 :style="{ height: '40%' }">
        <van-date-picker v-model="currentDate"
                         type="date"
                         :min-date="minDate"
                         :max-date="maxDate"
                         :formatter="formatter"
                         @confirm="onConfirm"
                         @cancel="onCancel"
                         :title="timeTitle" />
      </van-popup> -->
  </div>
</template>
<script>
export default { name: 'commentList' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
// import { formatDate } from '@/assets/js/utils.js'
import commentItem from './commentItem.vue'

const title = ref('评论列表')

// const show = ref(false)//筛选弹窗
const keyword = ref('')//搜索关键词 '',
// const showSkeleton = ref(true)//骨架屏
const loading = ref(false)//加载中
const finished = ref(false)//是否加载完成
const refreshing = ref(false)//下拉刷新
const pageNo = ref(1)//当前页码
const pageSize = ref(10)//每页条数
const total = ref(0)//总条数
const dataList = ref([])
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  businessCode: {
    type: String,
    default: ''
  }
})
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  setTimeout(() => {
    onRefresh()
  }, 100)
})
const getList = async () => {
  const params = {
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    keyword: keyword.value,
    businessCode: props.businessCode,
    businessId: props.id,
  }

  const { data, total: totals, code } = await api.zyConvenientlyCommentTreeList(params)
  if (code === 200) {
    dataList.value = data || []
    loading.value = false
    finished.value = total.value <= pageNo.value * pageSize.value
    refreshing.value = false
    total.value = totals
  } else {
    loading.value = false
    finished.value = true
    refreshing.value = false
  }
}
const callback = (e) => {
  console.log(e);
  dataList.value = dataList.value.filter(item => item.id !== e)
}
const reload = () => {
  onRefresh()
}
const onRefresh = () => {
  pageNo.value = 1
  getList()
}
const onLoad = () => {
  if (dataList.value.length < total.value) {
    pageNo.value++
    getList()
  } else {
    finished.value = true
  }
}

defineExpose({ getList })
</script>
<style lang="scss">
.commentList {
  background: #F4F5F9;
  overflow: auto;

  .title {
    background-color: #FFF;
    padding: 10px;

    .titleText {
      border-left: 3px solid var(--van-primary-color);
      padding-left: 10px;
      font-size: 16px;
      color: #333333;
      font-style: normal;
      height: 15px;
      line-height: 15px;
      text-transform: none;
    }
  }

  .btnBox {
    position: fixed;
    bottom: 40px;
    right: 20px;
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: var(--van-primary-color);
    border-radius: 20px 20px 20px 20px;
    color: #FFF;
  }

  .addBtnBox {
    background: #FFF;
    padding: 11px 12px;

    .addBtnImg {
      height: 108px;
    }
  }

  .searchBtn {
    width: 64px;
    height: 35px;
    background: #F1F1F1;
    border-radius: 18px 18px 18px 18px;
    text-align: center;
    line-height: 35px;
    font-size: 12px;
    color: #666666;
  }

  .popupBox {
    padding: 20px 20px;

    .sreenTitle {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .sreenBox {
      flex-wrap: wrap;
      margin: 10px 0 20px 0;

      .timeItemline {
        color: #C7C7C7;
        margin: 10px 5px 0 5px
      }

      .timeItemImg {
        width: 24px;
        height: 24px;
        margin: 0px 8px 0 8px;
      }

      .timeItemText {
        font-weight: 400;
        font-size: 14px;
        color: #AAAAAA;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .timeItem {
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
        align-items: center;
        border: 1px solid #F1F1F1;
        height: 40px;
        line-height: 40px;
        width: calc(50% - 10px);
      }

      .sreenItem {
        width: calc(50% - 10px);
        height: 40px;
        margin: 10px 5px;
        line-height: 40px;
        text-align: center;
        border-radius: 4px 4px 4px 4px;
        background: #F1F1F1;
        border: 1px solid #F1F1F1;
      }

      .active {
        background: #FFFFFF;
        color: #4488EB;
        border: 1px solid #4488EB;
      }
    }
  }
}
</style>
