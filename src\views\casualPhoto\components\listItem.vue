<template>
  <div class="listItem">
    <div class="flex_box">
      <div class="title text_two">{{ itemData.name }}</div>
      <span :class="['status', getStatus(itemData.processStateName)]">
        {{ itemData.processStateName }}
      </span>
    </div>
    <div class="header flex_box">
      <van-image :src="itemData.userImg"
                 alt="用户头像"
                 fit="cover"
                 class="avatar" />
      <div class="details">
        <div class="flex_box">
          <div class="name">{{ itemData.userName }}</div>
          <!-- <div v-if="itemData.userType"
               class="flex_box">
            <div class="tag"
                 v-for="item in itemData.userType.split(',')"
                 :key="item">{{ item }}</div>
          </div> -->
        </div>
        <div class="flex_box">
          <div class="time">{{ formatDate(itemData.createDate, 'YYYY-MM-DD ') }}</div>
          <div class="flex_box ">
            <div class="flex_box commentBox"
                 @click.stop="handlePraises(itemData)">
              <van-icon :name="itemData.hasClickPraises ? 'good-job' : 'good-job-o'"
                        :class="{ 'active': itemData.hasClickPraises }" />
              <div class="count"
                   :class="{ 'active': itemData.hasClickPraises }">{{ itemData.praisesCount }}</div>
            </div>
            <div class="flex_box commentBox">
              <van-icon name="comment-o" />
              <div class="count">{{ itemData.commentCount }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="content">
      <p>{{ itemData.content }}</p>
    </div> -->
    <div class="footer">
      <div class="flex_box">
        <p class="stationName"><van-icon name="home-o" />{{ itemData.stationName }}</p>
        <div class="flex_placeholder"></div>
        <p class="stationName"
           :class="getAnswerStatus(itemData)"
           v-if="itemData.answerEndDate"><van-icon name="clock-o" />答复期限:
          {{ formatDate(itemData.answerEndDate, 'YYYY-MM-DD') }}
        </p>
      </div>
      <div v-if="itemData.replydata && itemData.replydata.reply"
           class="reply">
        <div class="flex_box">
          <p class="reply-stationName">{{ itemData.replydata.stationName }}</p>
          <p class="reply-title">回复</p>
        </div>
        <p class="reply-content">{{ itemData.replydata.reply }}</p>
        <div class="flex_box reply-imgBox">
          <van-image v-for="(item, index) in itemData.replydata.imgList"
                     :src="item.url"
                     :key="item"
                     class="reply-img"
                     fit="cover"
                     @click.stop="openImagePreview(index)"></van-image>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'listItem' }
</script>
<script setup>
import api from '@/api'
import { ref } from 'vue'
import { showImagePreview } from 'vant'
import { formatDate } from '@/assets/js/utils.js'
const props = defineProps({
  itemData: {
    type: Object,
    default: () => { }
  }
})
const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
const handlePraises = async (itemData) => {
  const params = {
    form: {
      relationId: itemData.id,
      userId: user.value.id,
    }
  }
  itemData.hasClickPraises = !itemData.hasClickPraises
  if (itemData.hasClickPraises) {
    itemData.praisesCount++
    await api.zyConvenientlyBrowseAdd(params)
  } else {
    itemData.praisesCount--
    await api.zyConvenientlyBrowseDels(params)
  }
}
const openImagePreview = (index) => {
  const imgList = props.itemData.replydata.imgList.map(item => item.url)
  showImagePreview({
    images: imgList,
    startPosition: index
  })
}
const getStatus = (status) => {
  switch (status) {
    case '已回复':
      return 'replied'
    case '待办理':
      return 'pending'
    case '已转交':
      return 'returned'
    default:
      return ''
  }
}
const getAnswerStatus = (itemData) => {
  var now = new Date()
  var day = calculateDaysDifference(itemData.answerEndDate, now)
  if (day <= 5) {//红
    return 'color2'
  } else if (day > 5 && day <= 15) {//黄
    return 'color1'
  } else if (day > 15) {//绿
    return 'color3'
  }
}
const calculateDaysDifference = (dateString1, dateString2) => {
  const date1 = new Date(dateString1)
  const date2 = new Date(dateString2)
  const diffTime = Math.abs(date2.getTime() - date1.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}
</script>
<style lang="scss">
.listItem {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #F4F4F4;

  .title {
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0 0 8px;
    flex: 1;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    width: 100%;
    flex: 1;
  }

  .user-info {
    display: flex;
  }

  .avatar {
    width: 42px;
    height: 42px;
    border-radius: 4px 4px 4px 4px;
    margin-right: 8px;
    overflow: hidden;
  }

  .tag {
    height: 18px;
    margin-left: 5px;
    background: rgba(68, 136, 235, 0.2);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #4488EB;
    font-weight: 400;
    font-size: 12px;
    color: #4488EB;
    padding: 0px 5px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .details {
    width: 100%;
    flex: 1;
  }

  .name {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #666666;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
  }

  .role {
    font-size: 14px;
    color: #888;
    margin: 0;
    flex: 1;
  }

  .time {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    width: 100%;
    flex: 1;
  }

  .commentBox {
    margin-left: 15px;

    .count {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 12px;
      text-align: left;
      margin-left: 5px;
    }

    .active {
      color: #4488EB;
    }
  }

  .status {
    font-size: 14px;
    padding: 0 8px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    color: #fff;
    flex-shrink: 0;
  }

  .status.replied {
    background-color: #43DDBB;
  }

  .status.pending {
    background-color: #FF6665;
  }

  .status.returned {
    background-color: #EEAB32;
  }

  .content {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .footer {
    font-size: 12px;
    color: #888;

    .stationName {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .color1 {
      color: #EEAB32;
    }

    .color2 {
      color: #FF6665;
    }

    .color3 {
      color: #43DDBB;
    }
  }

  .reply {
    margin-top: 8px;
    padding: 14px;
    background: #F1F1F1;
    border-radius: 8px 8px 8px 8px;
  }

  .reply-stationName {
    font-weight: 400;
    font-size: 15px;
    color: #333333;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-right: 10px;
  }

  .reply-title {
    font-weight: 400;
    font-size: 15px;
  }

  .reply-content {
    margin-top: 8px;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .reply-imgBox {
    flex-wrap: wrap;
  }

  .reply-img {
    width: 73px;
    height: 73px;
    border-radius: 0px 0px 0px 0px;
    margin-bottom: 8px;
    margin-right: 4px;
  }
}
</style>
