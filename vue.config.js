const { defineConfig } = require('@vue/cli-service')
const { name } = require('./package.json')
const Version = new Date().getTime()
console.log(name)
process.env.VUE_APP_URL = process.env?.npm_config_url || ''
process.env.VUE_APP_CATALOG = process.env?.npm_config_catalog || ''
process.env.VUE_APP_NAME = name
module.exports = defineConfig({
  outputDir: `./${name}`,
  publicPath: './',
  productionSourceMap: process.env?.npm_config_map === 'true' ? true : false,
  transpileDependencies: true,
  css: {
    extract: {
      filename: `static/css/[name].${name}.${Version}.css`,
      chunkFilename: `static/css/[name].${name}.${Version}.css`
    },
    loaderOptions: {
      scss: { // 如果配置为"additionalData"无效，请到官网查阅最新配置信息
        additionalData: '@use "@/assets/scss/common.scss" as *;'
      }
    }
  },
  configureWebpack: {
    cache: {
      type: 'filesystem',
      allowCollectingMemory: true
    },
    output: {
      filename: `static/js/[name].${name}.${Version}.js`,
      chunkFilename: `static/js/[name].${name}.${Version}.js`
    }
  },
  devServer: {
    port: 8888,
    client: {
      overlay: false
    },
    // proxy: {
    //   '/api': {
    //     target: 'http://116.162.219.3:84/service-ssp',
    //     secure: false, // 关闭证书验证
    //     changeOrigin: true,
    //     pathRewrite: { '^/api': '' }
    //   },
    // },
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
})
